import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mr_garments_mobile/screens/distributor/distributor_homescreen/distributor_sidebar.dart';
import 'package:mr_garments_mobile/services/brand_service.dart';
import 'package:mr_garments_mobile/services/catalog_service.dart';
import 'package:mr_garments_mobile/services/category_service.dart';
import 'package:mr_garments_mobile/services/session_service.dart';
import 'package:mr_garments_mobile/screens/widgets/brands_section.dart';
import 'package:mr_garments_mobile/screens/widgets/brands_viewall.dart';
import 'package:mr_garments_mobile/screens/widgets/catalog_section.dart';
import 'package:mr_garments_mobile/screens/widgets/catalog_viewall.dart';
import 'package:mr_garments_mobile/screens/widgets/categories_section.dart';
import 'package:mr_garments_mobile/screens/widgets/categories_viewall.dart';
import 'package:mr_garments_mobile/screens/widgets/custom_bottom_nav_bar.dart';

class DistributorHomeScreen extends StatefulWidget {
  const DistributorHomeScreen({super.key});

  @override
  State<DistributorHomeScreen> createState() => _DistributorHomeScreenState();
}

class _DistributorHomeScreenState extends State<DistributorHomeScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final int _selectedIndex = 0;
  bool _showWelcomeCard = true;
  String _distributorName = 'Distributor';
  bool _isStaffUser = false;

  List<String> brandImages = [];
  List<Map<String, String>> categoryData = [];
  List<Map<String, String>> catalogList = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadDistributorName();
    _checkUserRole();
    _fetchHomeData();
    Future.delayed(const Duration(seconds: 10), () {
      setState(() => _showWelcomeCard = false);
    });
  }

  Future<void> _checkUserRole() async {
    try {
      final userRole = await SessionService.getUserRole();
      setState(() {
        _isStaffUser = userRole == 'staff';
        // Hide welcome card immediately for staff users
        if (_isStaffUser) {
          _showWelcomeCard = false;
        }
      });
    } catch (e) {
      // Keep default behavior if there's an error
    }
  }

  Future<void> _fetchHomeData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });
    try {
      // Fetch brands
      final brands = await BrandService.fetchBrands();
      brandImages =
          brands
              .map<String>(
                (b) =>
                    (b['image'] is List && b['image'].isNotEmpty)
                        ? b['image'][0] as String
                        : '',
              )
              .where((url) => url.isNotEmpty)
              .toList();
      // Fetch categories
      final categories = await CategoryService.fetchCategories();
      categoryData =
          categories
              .map<Map<String, String>>(
                (c) => {
                  'image': c['imageUrl'] as String? ?? '',
                  'title': c['categoryName'] as String? ?? '',
                },
              )
              .where((c) => c['image']!.isNotEmpty && c['title']!.isNotEmpty)
              .toList();
      // Fetch catalogs
      final catalogs = await CatalogService.fetchCatalogs();
      catalogList =
          catalogs
              .map<Map<String, String>>(
                (cat) => {
                  'image': cat['image'] as String? ?? '',
                  'brand': cat['brandName'] as String? ?? '',
                  'catalog': cat['catalogNumber'] as String? ?? '',
                },
              )
              .where(
                (cat) =>
                    cat['image']!.isNotEmpty &&
                    cat['brand']!.isNotEmpty &&
                    cat['catalog']!.isNotEmpty,
              )
              .toList();
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = e.toString();
      });
    }
  }

  void _loadDistributorName() async {
    try {
      final userName = await SessionService.getUserName();
      if (userName != null && userName.isNotEmpty) {
        setState(() {
          _distributorName = userName;
        });
      }
    } catch (e) {
      // Keep default name if there's an error
    }
  }

  void _navigateTo(String section) {
    if (section == 'Brands') {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (_) => const BrandsViewallScreen()),
      );
    } else if (section == 'Categories') {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (_) => const CategoriesViewall()),
      );
    } else if (section == 'Catalog') {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (_) => const CatalogViewall()),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: Colors.white,
      drawer: DistributorSidebar(),
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(90),
        child: AppBar(
          automaticallyImplyLeading: false,
          backgroundColor: const Color(0xFF00536B),
          elevation: 4,
          flexibleSpace: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: () => _scaffoldKey.currentState?.openDrawer(),
                    child: Container(
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                      ),
                      padding: const EdgeInsets.all(8),
                      child: const Icon(Icons.menu, color: Color(0xFF00536B)),
                    ),
                  ),
                  const SizedBox(width: 10),
                  Expanded(
                    child: TextField(
                      decoration: InputDecoration(
                        hintText: 'Search...',
                        hintStyle: GoogleFonts.poppins(color: Colors.white70),
                        prefixIcon: const Icon(
                          Icons.search,
                          color: Colors.white,
                        ),
                        filled: true,
                        fillColor: Colors.white.withAlpha(26),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(30),
                          borderSide: BorderSide.none,
                        ),
                        contentPadding: const EdgeInsets.symmetric(vertical: 0),
                      ),
                      style: const TextStyle(color: Colors.white),
                    ),
                  ),
                  const SizedBox(width: 10),
                  const Icon(
                    Icons.notifications_none,
                    color: Colors.white,
                    size: 25,
                  ),
                  const SizedBox(width: 10),
                  // const CircleAvatar(
                  //   backgroundImage: AssetImage('assets/profile.jpg'),
                  //   radius: 18,
                  // ),
                ],
              ),
            ),
          ),
        ),
      ),

      body: SingleChildScrollView(
        child: Container(
          padding: const EdgeInsets.all(14),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AnimatedOpacity(
                opacity: _showWelcomeCard ? 1.0 : 0.0,
                duration: const Duration(milliseconds: 600),
                child:
                    _showWelcomeCard
                        ? Container(
                          margin: const EdgeInsets.symmetric(vertical: 8),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(20),
                            color: const Color.fromARGB(255, 238, 236, 236),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withAlpha(13),
                                blurRadius: 10,
                                offset: const Offset(0, 4),
                              ),
                            ],
                            border: Border.all(
                              color: Colors.white.withAlpha(55),
                            ),
                          ),
                          padding: const EdgeInsets.all(20),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Hi, $_distributorName',
                                style: GoogleFonts.poppins(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                  color: const Color(0xFFF2A738),
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'Welcome to Distributor Panel!',
                                style: GoogleFonts.poppins(
                                  fontSize: 22,
                                  fontWeight: FontWeight.w600,
                                  color: const Color(0xFF005368),
                                ),
                              ),
                            ],
                          ),
                        )
                        : const SizedBox.shrink(),
              ),
              const SizedBox(height: 20),
              BrandsSection(
                brandImages: brandImages,
                onViewAll: () => _navigateTo('Brands'),
              ),
              const SizedBox(height: 30),
              CategoriesSection(
                categories: categoryData,
                onViewAll: () => _navigateTo('Categories'),
              ),
              const SizedBox(height: 35),
              CatalogSection(
                catalogList: catalogList,
                onViewAll: () => _navigateTo('Catalog'),
              ),
            ],
          ),
        ),
      ),

      bottomNavigationBar: CustomBottomNavBar(currentIndex: _selectedIndex),
    );
  }
}
