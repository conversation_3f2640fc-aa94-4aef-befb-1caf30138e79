import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:mr_garments_mobile/models/message.dart';
import 'package:mr_garments_mobile/models/chat.dart' show Chat, ChatType;
import 'package:mr_garments_mobile/models/chat_user.dart';
import 'package:mr_garments_mobile/services/chat_service.dart';
import 'package:mr_garments_mobile/services/chat_service_update.dart';
import 'package:mr_garments_mobile/screens/chat/member_chat_inbox.dart';
import 'package:mr_garments_mobile/services/session_service.dart';
import 'package:mr_garments_mobile/utils/snackbar.dart';
import 'package:mr_garments_mobile/screens/catalog/add_catalog.dart';
import 'package:mr_garments_mobile/screens/order/create_order.dart';
import 'package:share_plus/share_plus.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:mr_garments_mobile/utils/message_utils.dart';

class MultiMessageActionsBottomSheet extends StatelessWidget {
  final List<Message> selectedMessages;
  final String chatId;
  final bool isGroup;
  final VoidCallback onActionComplete;

  const MultiMessageActionsBottomSheet({
    super.key,
    required this.selectedMessages,
    required this.chatId,
    required this.isGroup,
    required this.onActionComplete,
  });

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final maxHeight = screenHeight * 0.8;

    // Analyze selected messages to determine available actions
    final hasOnlyImages = selectedMessages.every(
      (m) => m.type == MessageType.image,
    );
    final hasOnlyCatalogs = selectedMessages.every(
      (m) => m.type == MessageType.catalog,
    );

    // Action visibility rules:
    // - Share and Forward: always available
    // - Delete: always available
    // - Create Order: only when ALL selected messages are catalogs
    // - Create Catalog: only when ALL selected messages are images (no text, no catalogs, no other types)

    return Container(
      constraints: BoxConstraints(maxHeight: maxHeight),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${selectedMessages.length} Messages Selected',
                  style: GoogleFonts.poppins(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF005368),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(
                    LucideIcons.x,
                    color: Color(0xFF005368),
                    size: 20,
                  ),
                ),
              ],
            ),
          ),

          const Divider(),

          // Scrollable action items
          Flexible(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // Share outside - always available
                  _buildActionItem(
                    context,
                    icon: LucideIcons.share2,
                    title: 'Share outside',
                    onTap: () {
                      Navigator.pop(context);
                      _handleShareOutside(context);
                    },
                  ),

                  // Forward - always available
                  _buildActionItem(
                    context,
                    icon: LucideIcons.forward,
                    title: 'Forward',
                    onTap: () {
                      Navigator.pop(context);
                      _handleForward(context);
                    },
                  ),

                  // Delete - always available
                  _buildActionItem(
                    context,
                    icon: LucideIcons.trash2,
                    title: 'Delete',
                    color: Colors.red,
                    onTap: () {
                      Navigator.pop(context);
                      _showDeleteConfirmation(context);
                    },
                  ),

                  // Create Order - only when ALL selected messages are catalogs
                  if (hasOnlyCatalogs)
                    _buildActionItem(
                      context,
                      icon: LucideIcons.shoppingBag,
                      title: 'Create Order',
                      onTap: () {
                        Navigator.pop(context);
                        _handleCreateOrder(context);
                      },
                    ),

                  // Create Catalog - only when ALL selected messages are images
                  if (hasOnlyImages)
                    _buildActionItem(
                      context,
                      icon: LucideIcons.bookOpen,
                      title: 'Create Catalog',
                      onTap: () {
                        Navigator.pop(context);
                        _handleCreateCatalog(context);
                      },
                    ),

                  // Bottom padding for safe area
                  SizedBox(height: MediaQuery.of(context).padding.bottom + 20),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Color? color,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        child: Row(
          children: [
            Icon(icon, color: color ?? const Color(0xFF005368), size: 20),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                title,
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  color: color ?? Colors.black87,
                ),
              ),
            ),
            Icon(LucideIcons.chevronRight, color: Colors.grey[400], size: 16),
          ],
        ),
      ),
    );
  }

  void _handleShareOutside(BuildContext context) async {
    try {
      // Combine all messages into a single share content
      String shareContent = '';
      List<XFile> filesToShare = [];

      for (final message in selectedMessages) {
        if (message.type == MessageType.text && message.text != null) {
          shareContent += '${message.text}\n\n';
        } else if (message.type == MessageType.image &&
            message.mediaUrl != null) {
          // Download and add image to files
          try {
            final response = await http.get(Uri.parse(message.mediaUrl!));
            if (response.statusCode == 200) {
              final tempDir = await getTemporaryDirectory();
              final fileName = 'shared_image_${message.id}.jpg';
              final file = File('${tempDir.path}/$fileName');
              await file.writeAsBytes(response.bodyBytes);
              filesToShare.add(XFile(file.path));

              if (message.text != null && message.text!.isNotEmpty) {
                shareContent += '${message.text}\n\n';
              }
            }
          } catch (e) {
            shareContent += '📷 Image\n\n';
          }
        } else if (message.type == MessageType.catalog) {
          shareContent += '📚 Catalog shared\n\n';
        }
      }

      shareContent +=
          'Shared from Mr. Garments Mobile App\nDownload: https://play.google.com/store/apps/details?id=com.braincave.mrgarments';

      if (filesToShare.isNotEmpty) {
        await Share.shareXFiles(filesToShare, text: shareContent);
      } else {
        await Share.share(shareContent);
      }

      if (context.mounted) {
        AppSnackbar.showSuccess(context, 'Messages shared successfully!');
        onActionComplete();
      }
    } catch (e) {
      if (context.mounted) {
        AppSnackbar.showError(
          context,
          'Failed to share messages: ${e.toString()}',
        );
      }
    }
  }

  void _handleForward(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => MultiMessageForwardScreen(
              messages: selectedMessages,
              chatId: chatId,
              onForwardComplete: () {
                AppSnackbar.showSuccess(
                  context,
                  'Messages forwarded successfully!',
                );
                onActionComplete();
              },
            ),
      ),
    );
  }

  void _handleCreateOrder(BuildContext context) {
    // Extract catalog data from selected messages
    final catalogMessages =
        selectedMessages.where((m) => m.type == MessageType.catalog).toList();

    if (catalogMessages.isEmpty) {
      AppSnackbar.showError(context, 'No catalog messages found in selection');
      return;
    }

    // Navigate to create order screen with catalog data
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) =>
                CreateOrderScreen(preSelectedCatalogs: catalogMessages),
      ),
    ).then((_) {
      onActionComplete();
    });
  }

  void _handleCreateCatalog(BuildContext context) {
    // Extract image URLs from selected messages
    final imageUrls =
        selectedMessages
            .where((m) => m.type == MessageType.image && m.mediaUrl != null)
            .map((m) => m.mediaUrl!)
            .toList();

    // print('Debug: Selected messages count: ${selectedMessages.length}');
    // print(
    //   'Debug: Image messages found: ${selectedMessages.where((m) => m.type == MessageType.image).length}',
    // );
    // print('Debug: Image URLs extracted: ${imageUrls.length}');
    // print('Debug: Image URLs: $imageUrls');

    if (imageUrls.isEmpty) {
      AppSnackbar.showError(context, 'No images found in selected messages');
      return;
    }

    // Navigate to create catalog screen with pre-selected images
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddCatalogScreen(preSelectedImageUrls: imageUrls),
      ),
    ).then((_) {
      onActionComplete();
    });
  }

  void _showDeleteConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => FutureBuilder<String?>(
            future: SessionService.getUserRole(),
            builder: (context, snapshot) {
              final userRole = snapshot.data;
              final isAdmin = userRole == 'admin';
              final messageCount = selectedMessages.length;
              final messagePlural = messageCount > 1 ? 's' : '';

              return AlertDialog(
                title: Text(
                  'Delete Messages',
                  style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
                ),
                content: Text(
                  isAdmin
                      ? 'Are you sure you want to delete $messageCount message$messagePlural for everyone?'
                      : 'Are you sure you want to delete $messageCount message$messagePlural for yourself?',
                  style: GoogleFonts.poppins(),
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text(
                      'Cancel',
                      style: GoogleFonts.poppins(color: Colors.grey[600]),
                    ),
                  ),
                  TextButton(
                    onPressed: () async {
                      Navigator.pop(context);
                      await _deleteMessages(context);
                    },
                    child: Text(
                      'Delete',
                      style: GoogleFonts.poppins(color: Colors.red),
                    ),
                  ),
                ],
              );
            },
          ),
    );
  }

  Future<void> _deleteMessages(BuildContext context) async {
    try {
      final userRole = await SessionService.getUserRole();
      final isAdmin = userRole == 'admin';

      for (final message in selectedMessages) {
        await ChatService.deleteMessage(chatId, message.id);
      }

      if (context.mounted) {
        final messageCount = selectedMessages.length;
        final messagePlural = messageCount > 1 ? 's' : '';
        final successMessage =
            isAdmin
                ? '$messageCount message$messagePlural deleted for everyone'
                : '$messageCount message$messagePlural deleted for you';

        AppSnackbar.showSuccess(context, successMessage);
        onActionComplete();
      }
    } catch (e) {
      if (context.mounted) {
        AppSnackbar.showError(
          context,
          'Failed to delete messages: ${e.toString()}',
        );
      }
    }
  }
}

// Multi-Message Forward Dialog Widget
class MultiMessageForwardDialog extends StatefulWidget {
  final List<Message> messages;
  final String chatId;
  final VoidCallback onForwardComplete;

  const MultiMessageForwardDialog({
    super.key,
    required this.messages,
    required this.chatId,
    required this.onForwardComplete,
  });

  @override
  State<MultiMessageForwardDialog> createState() =>
      _MultiMessageForwardDialogState();
}

class _MultiMessageForwardDialogState extends State<MultiMessageForwardDialog> {
  List<Chat> availableChats = [];
  Set<String> selectedChatIds = {};
  bool isLoading = true;
  String? currentUserId;

  @override
  void initState() {
    super.initState();
    _loadChats();
  }

  Future<void> _loadChats() async {
    try {
      final userId = await SessionService.getUserId();
      currentUserId = userId?.toString();
      if (currentUserId == null) {
        if (mounted) {
          Navigator.pop(context);
          AppSnackbar.showError(context, 'User not logged in');
        }
        return;
      }

      // Get user's role first
      final userRole = await SessionService.getUserRole();

      // Get user's chats stream and take the first snapshot
      final chatsStream = ChatService.getUserChatsStream(currentUserId!);
      await for (final chats in chatsStream) {
        if (mounted) {
          final filteredChats = <Chat>[];

          // Process each chat
          for (final chat in chats) {
            // Skip current chat
            if (chat.id == widget.chatId) continue;

            // Admin can forward to all chats
            if (userRole == 'admin') {
              filteredChats.add(chat);
              continue;
            }

            // Non-admin users can only forward to individual chats with admin
            if (chat.type == ChatType.individual) {
              for (final memberId in chat.memberIds) {
                if (memberId == currentUserId) continue;
                final member = await ChatService.getUser(memberId);
                if (member?.role == 'admin') {
                  filteredChats.add(chat);
                  break;
                }
              }
            }
          }

          setState(() {
            availableChats = filteredChats;
            isLoading = false;
          });
        }
        break; // Take only the first snapshot
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
        AppSnackbar.showError(context, 'Failed to load chats: ${e.toString()}');
      }
    }
  }

  void _toggleChatSelection(String chatId) {
    setState(() {
      if (selectedChatIds.contains(chatId)) {
        selectedChatIds.remove(chatId);
      } else {
        selectedChatIds.add(chatId);
      }
    });
  }

  Future<void> _forwardMessages() async {
    if (selectedChatIds.isEmpty) {
      AppSnackbar.showInfo(context, 'Please select at least one chat');
      return;
    }

    try {
      // Forward each message to selected chats
      for (final message in widget.messages) {
        await ChatService.forwardMessage(
          fromChatId: widget.chatId,
          messageId: message.id,
          toChatIds: selectedChatIds.toList(),
        );
      }

      if (mounted) {
        Navigator.pop(context);
        AppSnackbar.showSuccess(
          context,
          '${widget.messages.length} message${widget.messages.length > 1 ? 's' : ''} forwarded to ${selectedChatIds.length} chat${selectedChatIds.length > 1 ? 's' : ''}',
        );
        widget.onForwardComplete();
      }
    } catch (e) {
      if (mounted) {
        AppSnackbar.showError(
          context,
          'Failed to forward messages: ${e.toString()}',
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Forward Messages',
                  style: GoogleFonts.poppins(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF005368),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(LucideIcons.x, color: Color(0xFF005368)),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Chat list
            Expanded(
              child:
                  isLoading
                      ? const Center(child: CircularProgressIndicator())
                      : availableChats.isEmpty
                      ? Center(
                        child: Text(
                          'No chats available',
                          style: GoogleFonts.poppins(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                      )
                      : ListView.builder(
                        itemCount: availableChats.length,
                        itemBuilder: (context, index) {
                          final chat = availableChats[index];
                          final isSelected = selectedChatIds.contains(chat.id);
                          return _buildChatItem(chat, isSelected);
                        },
                      ),
            ),

            const SizedBox(height: 16),

            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text(
                    'Cancel',
                    style: GoogleFonts.poppins(color: Colors.grey[600]),
                  ),
                ),
                const SizedBox(width: 12),
                ElevatedButton(
                  onPressed:
                      selectedChatIds.isNotEmpty ? _forwardMessages : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF005368),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(
                    'Forward (${selectedChatIds.length})',
                    style: GoogleFonts.poppins(fontWeight: FontWeight.w500),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChatItem(Chat chat, bool isSelected) {
    return InkWell(
      onTap: () => _toggleChatSelection(chat.id),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        margin: const EdgeInsets.symmetric(vertical: 2),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF005368).withOpacity(0.1) : null,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            // Selection indicator
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: isSelected ? const Color(0xFF005368) : Colors.grey,
                  width: 2,
                ),
                color:
                    isSelected ? const Color(0xFF005368) : Colors.transparent,
              ),
              child:
                  isSelected
                      ? const Icon(
                        LucideIcons.check,
                        size: 12,
                        color: Colors.white,
                      )
                      : null,
            ),

            const SizedBox(width: 12),

            // Chat info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    chat.getDisplayName(currentUserId ?? ''),
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                    ),
                  ),
                  if (chat.lastMessage != null)
                    Text(
                      chat.lastMessage!.text ?? 'Media message',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Full-Screen Multi-Message Forward Screen
class MultiMessageForwardScreen extends StatefulWidget {
  final List<Message> messages;
  final String chatId;
  final VoidCallback onForwardComplete;

  const MultiMessageForwardScreen({
    super.key,
    required this.messages,
    required this.chatId,
    required this.onForwardComplete,
  });

  @override
  State<MultiMessageForwardScreen> createState() =>
      _MultiMessageForwardScreenState();
}

class _MultiMessageForwardScreenState extends State<MultiMessageForwardScreen>
    with SingleTickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  List<Chat> availableIndividualChats = [];
  List<Chat> availableGroups = [];
  List<Chat> filteredIndividualChats = [];
  List<Chat> filteredGroups = [];
  List<ChatUser> availableUsers = [];
  List<ChatUser> filteredUsers = [];
  Set<String> selectedChatIds = {};
  bool isLoading = true;
  bool isSearching = false;
  String? currentUserId;
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadChatsAndUsers();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadChatsAndUsers() async {
    try {
      final userId = await SessionService.getUserId();
      currentUserId = userId?.toString();

      if (currentUserId == null) {
        if (mounted) {
          Navigator.pop(context);
          AppSnackbar.showError(context, 'User not logged in');
        }
        return;
      }

      // Load both existing chats and all available users
      await Future.wait([_loadExistingChats(), _loadAllUsers()]);

      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
        AppSnackbar.showError(context, 'Failed to load data: ${e.toString()}');
      }
    }
  }

  Future<void> _loadExistingChats() async {
    try {
      // Get all chats with role-based filtering
      final chatsStream = ChatService.getUserChatsStream(currentUserId!);
      await for (final chats in chatsStream) {
        if (mounted) {
          final filteredChats =
              chats.where((chat) => chat.id != widget.chatId).toList();
          setState(() {
            // Split into individual chats and groups
            availableIndividualChats =
                filteredChats
                    .where((chat) => chat.type == ChatType.individual)
                    .toList();
            availableGroups =
                filteredChats
                    .where((chat) => chat.type == ChatType.group)
                    .toList();
            // Initialize filtered lists
            filteredIndividualChats = availableIndividualChats;
            filteredGroups = availableGroups;
          });
        }
        break; // Take only the first snapshot
      }
    } catch (e) {
      // Handle error silently, will be caught by parent method
      rethrow;
    }
  }

  Future<void> _loadAllUsers() async {
    try {
      // Get all available users from the backend
      final users = await ChatService.getAllUsers();
      if (mounted) {
        setState(() {
          availableUsers = users;
          filteredUsers = users;
        });
      }
    } catch (e) {
      // Handle error silently, will be caught by parent method
      rethrow;
    }
  }

  void _searchChats(String query) {
    if (query.isEmpty) {
      setState(() {
        filteredIndividualChats = availableIndividualChats;
        filteredGroups = availableGroups;
        filteredUsers = availableUsers;
        isSearching = false;
      });
      return;
    }

    setState(() {
      isSearching = true;
      // Filter individual chats
      filteredIndividualChats =
          availableIndividualChats
              .where(
                (chat) => chat
                    .getDisplayName(currentUserId!)
                    .toLowerCase()
                    .contains(query.toLowerCase()),
              )
              .toList();
      // Filter groups
      filteredGroups =
          availableGroups
              .where(
                (chat) => chat
                    .getDisplayName(currentUserId!)
                    .toLowerCase()
                    .contains(query.toLowerCase()),
              )
              .toList();
      // Filter users
      filteredUsers =
          availableUsers
              .where(
                (user) =>
                    user.name.toLowerCase().contains(query.toLowerCase()) ||
                    user.email.toLowerCase().contains(query.toLowerCase()),
              )
              .toList();
    });
  }

  void _toggleChatSelection(String chatId) {
    setState(() {
      if (selectedChatIds.contains(chatId)) {
        selectedChatIds.remove(chatId);
      } else {
        selectedChatIds.add(chatId);
      }
    });
  }

  void _toggleUserSelection(ChatUser user) async {
    try {
      // Check if there's already an existing chat with this user
      final existingChat = availableIndividualChats.firstWhere(
        (chat) => chat.memberIds.contains(user.id),
        orElse:
            () => Chat(
              id: '',
              memberIds: [],
              type: ChatType.individual,
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
              isActive: true,
            ),
      );

      String chatId;
      if (existingChat.id.isNotEmpty) {
        // Use existing chat
        chatId = existingChat.id;
      } else {
        // Create new chat
        chatId = await ChatService.createIndividualChat(user.id);
      }

      // Toggle selection
      setState(() {
        if (selectedChatIds.contains(chatId)) {
          selectedChatIds.remove(chatId);
        } else {
          selectedChatIds.add(chatId);
        }
      });
    } catch (e) {
      if (mounted) {
        AppSnackbar.showError(
          context,
          'Failed to select user: ${e.toString()}',
        );
      }
    }
  }

  Future<void> _navigateToChat(String chatId) async {
    try {
      // Get chat details to determine chat name and type
      final chatDoc = await ChatService.getChat(chatId);
      if (chatDoc == null) return;

      String chatName;
      bool isGroup = chatDoc.type == ChatType.group;

      if (isGroup) {
        chatName = chatDoc.groupName ?? 'Group Chat';
      } else {
        chatName = chatDoc.getDisplayName(currentUserId ?? '');
      }

      // Navigate to the individual chat screen
      if (mounted) {
        // Use Future.microtask to avoid navigator assertion error
        Future.microtask(() {
          if (mounted) {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(
                builder:
                    (context) => MemberChatInbox(
                      chatId: chatId,
                      chatName: chatName,
                      isGroup: isGroup,
                    ),
              ),
            );
          }
        });
      }
    } catch (e) {
      // Handle error silently or show a snackbar
      if (mounted) {
        AppSnackbar.showError(context, 'Failed to open chat');
      }
    }
  }

  Future<void> _forwardMessages() async {
    if (selectedChatIds.isEmpty) {
      AppSnackbar.showInfo(context, 'Please select at least one recipient');
      return;
    }

    try {
      // Get all messages with their relationships intact
      final messageGroups = await _getMessageGroupsWithRelationships(widget.messages);
      
      // Forward messages to each selected chat
      for (final chatId in selectedChatIds) {
        for (final group in messageGroups) {
          if (group.length == 1) {
            // Single message - use regular forwarding
            await ChatService.forwardMessage(
              fromChatId: widget.chatId,
              messageId: group.first.id,
              toChatIds: [chatId],
            );
          } else {
            // Group of messages - forward as a group
            await MessageUtils.forwardMessageGroup(
              messages: group,
              toChatId: chatId,
              currentUserId: currentUserId!,
              currentUserName: await SessionService.getUserName() ?? 'User',
              chatsCollection: FirebaseFirestore.instance.collection('chats'),
              preserveGroup: true, // Ensure group relationship is preserved
            );
          }
        }
      }

      if (mounted) {
        Navigator.pop(context);
        int totalMessages = messageGroups.fold(0, (sum, group) => sum + group.length);
        AppSnackbar.showSuccess(
          context,
          '$totalMessages message${totalMessages > 1 ? 's' : ''} forwarded to ${selectedChatIds.length} chat${selectedChatIds.length > 1 ? 's' : ''}',
        );
        widget.onForwardComplete();
      }
    } catch (e) {
      if (mounted) {
        AppSnackbar.showError(
          context,
          'Failed to forward messages: ${e.toString()}',
        );
      }
    }
  }

  /// Gets messages with their relationships preserved, especially for image groups
  Future<List<List<Message>>> _getMessageGroupsWithRelationships(List<Message> messages) async {
    final List<List<Message>> groupedMessages = [];
    final Set<String> processedMessageIds = {};

    for (final message in messages) {
      if (processedMessageIds.contains(message.id)) continue;

      if (message.type == MessageType.image) {
        // For image messages, check for group relationships
        List<Message> groupMessages = [];
        
        // First check if this message has an imageGroupId in metadata
        final imageGroupId = message.metadata?['imageGroupId'];
        
        if (imageGroupId != null) {
          // Find all messages in the selection with the same imageGroupId
          groupMessages = messages.where((m) => 
            m.type == MessageType.image && 
            m.metadata?['imageGroupId'] == imageGroupId
          ).toList();
          
          // Sort by timestamp to maintain order
          groupMessages.sort((a, b) => a.timestamp.compareTo(b.timestamp));
        } else {
          // Fallback to time-based grouping for messages without imageGroupId
          groupMessages = MessageUtils.getImageGroupMessagesFromList(
            messages: messages,
            targetMessage: message,
          );
          
          // If we only found one message and it's part of a larger group,
          // try to fetch the complete group from the chat
          if (groupMessages.length == 1) {
            try {
              final completeGroup = await ChatService.getImageGroupMessages(
                chatId: widget.chatId, 
                messageId: message.id
              );
              if (completeGroup.length > 1) {
                // Filter the complete group to only include messages that are in our selection
                final selectedMessageIds = messages.map((m) => m.id).toSet();
                final filteredGroup = completeGroup.where((m) => selectedMessageIds.contains(m.id)).toList();
                if (filteredGroup.length > 1) {
                  groupMessages = filteredGroup;
                }
              }
            } catch (e) {
              // If fetching complete group fails, use what we have
              print('Warning: Failed to fetch complete image group: $e');
            }
          }
        }

        // Add all messages in this group
        groupedMessages.add(groupMessages);
        processedMessageIds.addAll(groupMessages.map((m) => m.id));
      } else {
        // Non-image message - add as single message
        groupedMessages.add([message]);
        processedMessageIds.add(message.id);
      }
    }

    return groupedMessages;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: const Icon(LucideIcons.arrowLeft, color: Color(0xFF005368)),
        ),
        title: Text(
          'Forward Messages',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF005368),
          ),
        ),
        actions: [
          if (selectedChatIds.isNotEmpty)
            TextButton(
              onPressed: _forwardMessages,
              child: Text(
                'Forward (${selectedChatIds.length})',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF005368),
                ),
              ),
            ),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              onChanged: _searchChats,
              decoration: InputDecoration(
                hintText: 'Search chats...',
                hintStyle: GoogleFonts.poppins(color: Colors.grey[500]),
                prefixIcon: const Icon(
                  LucideIcons.search,
                  color: Color(0xFF005368),
                  size: 20,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Color(0xFF005368)),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),
          ),

          // Tab Bar
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(25),
            ),
            child: TabBar(
              controller: _tabController,
              labelColor: Colors.white,
              unselectedLabelColor: Colors.grey[600],
              indicator: BoxDecoration(
                color: const Color(0xFF005368),
                borderRadius: BorderRadius.circular(25),
              ),
              indicatorSize: TabBarIndicatorSize.tab,
              dividerColor: Colors.transparent,
              tabs: [
                Tab(
                  height: 45,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(LucideIcons.user, size: 18),
                        const SizedBox(width: 8),
                        Text(
                          'Individual',
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Tab(
                  height: 45,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(LucideIcons.users, size: 18),
                        const SizedBox(width: 8),
                        Text(
                          'Groups',
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 12),

          // Chat lists
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // Individual Users Tab
                _buildUserList(filteredUsers),
                // Groups Tab
                _buildChatList(filteredGroups),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserList(List<ChatUser> users) {
    if (isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: Color(0xFF005368)),
      );
    }

    if (users.isEmpty) {
      return _buildEmptyState(
        icon: LucideIcons.users,
        title: isSearching ? 'No users found' : 'No users available',
        subtitle:
            isSearching ? 'Try a different search term' : 'No users to show',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: users.length,
      itemBuilder: (context, index) {
        final user = users[index];
        // Check if there's an existing chat with this user
        final existingChat = availableIndividualChats.firstWhere(
          (chat) => chat.memberIds.contains(user.id),
          orElse:
              () => Chat(
                id: '',
                memberIds: [],
                type: ChatType.individual,
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
                isActive: true,
              ),
        );

        final chatId =
            existingChat.id.isNotEmpty ? existingChat.id : 'new_${user.id}';
        final isSelected =
            selectedChatIds.contains(chatId) ||
            selectedChatIds.any((id) => id.contains(user.id));

        return _buildUserItem(user, isSelected);
      },
    );
  }

  Widget _buildChatList(List<Chat> chats) {
    if (isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: Color(0xFF005368)),
      );
    }

    if (chats.isEmpty) {
      return _buildEmptyState(
        icon: LucideIcons.users,
        title: isSearching ? 'No chats found' : 'No chats available',
        subtitle:
            isSearching ? 'Try a different search term' : 'No chats to show',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: chats.length,
      itemBuilder: (context, index) {
        final chat = chats[index];
        final isSelected = selectedChatIds.contains(chat.id);
        return _buildChatItem(chat, isSelected);
      },
    );
  }

  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 64, color: Colors.grey.withOpacity(0.5)),
          const SizedBox(height: 16),
          Text(
            title,
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: GoogleFonts.poppins(fontSize: 14, color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  Widget _buildChatItem(Chat chat, bool isSelected) {
    return InkWell(
      onTap: () => _toggleChatSelection(chat.id),
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 4),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? const Color(0xFF005368).withOpacity(0.1)
                  : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? const Color(0xFF005368) : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            // Chat avatar
            CircleAvatar(
              radius: 24,
              backgroundColor: const Color(0xFF005368),
              backgroundImage:
                  chat.getDisplayImage(currentUserId!) != null
                      ? NetworkImage(chat.getDisplayImage(currentUserId!)!)
                      : null,
              child:
                  chat.getDisplayImage(currentUserId!) == null
                      ? Icon(
                        chat.type == ChatType.group
                            ? LucideIcons.users
                            : LucideIcons.user,
                        color: Colors.white,
                        size: 24,
                      )
                      : null,
            ),

            const SizedBox(width: 12),

            // Chat details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    chat.getDisplayName(currentUserId!),
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: const Color(0xFF005368),
                    ),
                  ),
                  if (chat.lastMessage != null)
                    Text(
                      _getMessagePreview(chat.lastMessage!),
                      style: GoogleFonts.poppins(
                        fontSize: 13,
                        color: Colors.grey[600],
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                ],
              ),
            ),

            // Selection indicator
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color:
                      isSelected ? const Color(0xFF005368) : Colors.grey[400]!,
                  width: 2,
                ),
                color:
                    isSelected ? const Color(0xFF005368) : Colors.transparent,
              ),
              child:
                  isSelected
                      ? const Icon(
                        LucideIcons.check,
                        size: 16,
                        color: Colors.white,
                      )
                      : null,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserItem(ChatUser user, bool isSelected) {
    return InkWell(
      onTap: () => _toggleUserSelection(user),
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 4),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? const Color(0xFF005368).withValues(alpha: 0.1)
                  : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? const Color(0xFF005368) : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            // User avatar
            CircleAvatar(
              radius: 24,
              backgroundColor: const Color(0xFF005368),
              backgroundImage:
                  user.profileImageUrl != null &&
                          user.profileImageUrl!.isNotEmpty
                      ? NetworkImage(user.profileImageUrl!)
                      : null,
              child:
                  user.profileImageUrl == null || user.profileImageUrl!.isEmpty
                      ? Icon(LucideIcons.user, color: Colors.white, size: 24)
                      : null,
            ),

            const SizedBox(width: 12),

            // User details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    user.name,
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: const Color(0xFF005368),
                    ),
                  ),
                  Text(
                    user.role.toUpperCase(),
                    style: GoogleFonts.poppins(
                      fontSize: 13,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),

            // Selection indicator
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color:
                      isSelected ? const Color(0xFF005368) : Colors.grey[400]!,
                  width: 2,
                ),
                color:
                    isSelected ? const Color(0xFF005368) : Colors.transparent,
              ),
              child:
                  isSelected
                      ? const Icon(
                        LucideIcons.check,
                        size: 16,
                        color: Colors.white,
                      )
                      : null,
            ),
          ],
        ),
      ),
    );
  }

  String _getMessagePreview(Message message) {
    switch (message.type) {
      case MessageType.text:
        return message.text ?? 'Message';
      case MessageType.image:
        return '📷 Image';
      case MessageType.video:
        return '🎥 Video';
      case MessageType.audio:
        return '🎵 Audio';
      case MessageType.file:
        return '📄 ${message.fileName ?? 'File'}';
      case MessageType.location:
        return '📍 Location';
      case MessageType.contact:
        return '👤 Contact';
      case MessageType.order:
        return '🛍️ Order';
      case MessageType.catalog:
        return '📚 Catalog';
    }
  }
}
