import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:mr_garments_mobile/models/message.dart';
import 'package:mr_garments_mobile/models/chat.dart';
import 'package:mr_garments_mobile/services/chat_service.dart';
import 'package:mr_garments_mobile/services/session_service.dart';
import 'package:mr_garments_mobile/utils/snackbar.dart';
import 'package:share_plus/share_plus.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'dart:io';
// import 'dart:typed_data';

class MessageActionsBottomSheet extends StatelessWidget {
  final Message message;
  final String chatId;
  final bool isGroup;

  const MessageActionsBottomSheet({
    super.key,
    required this.message,
    required this.chatId,
    required this.isGroup,
  });

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final maxHeight = screenHeight * 0.8; // Maximum 80% of screen height

    return Container(
      constraints: BoxConstraints(maxHeight: maxHeight),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Share Content',
                  style: GoogleFonts.poppins(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF005368),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(
                    LucideIcons.x,
                    color: Color(0xFF005368),
                    size: 20,
                  ),
                ),
              ],
            ),
          ),

          const Divider(),

          // Scrollable action items
          Flexible(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  _buildActionItem(
                    context,
                    icon: LucideIcons.share2,
                    title: 'Share outside',
                    onTap: () {
                      Navigator.pop(context);
                      _handleShareOutside(context);
                    },
                  ),

                  _buildActionItem(
                    context,
                    icon: LucideIcons.forward,
                    title: 'Forward',
                    onTap: () {
                      Navigator.pop(context);
                      _handleForward(context);
                    },
                  ),

                  _buildActionItem(
                    context,
                    icon: LucideIcons.trash2,
                    title: 'Delete',
                    color: Colors.red,
                    onTap: () {
                      Navigator.pop(context);
                      _showDeleteOptions(context);
                    },
                  ),

                  _buildActionItem(
                    context,
                    icon: LucideIcons.shoppingBag,
                    title: 'Create Order',
                    onTap: () {
                      Navigator.pop(context);
                      _handleCreateOrder(context);
                    },
                  ),

                  _buildActionItem(
                    context,
                    icon: LucideIcons.bookOpen,
                    title: 'Create Catalog',
                    onTap: () {
                      Navigator.pop(context);
                      _handleCreateCatalog(context);
                    },
                  ),

                  _buildActionItem(
                    context,
                    icon: LucideIcons.info,
                    title: 'Info',
                    onTap: () {
                      Navigator.pop(context);
                      _showMessageInfo(context);
                    },
                  ),

                  // Bottom padding for safe area
                  SizedBox(height: MediaQuery.of(context).padding.bottom + 20),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Color? color,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        child: Row(
          children: [
            Icon(icon, color: color ?? const Color(0xFF005368), size: 20),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                title,
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  color: color ?? Colors.black87,
                ),
              ),
            ),
            Icon(LucideIcons.chevronRight, color: Colors.grey[400], size: 16),
          ],
        ),
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => FutureBuilder<String?>(
            future: SessionService.getUserRole(),
            builder: (context, snapshot) {
              final userRole = snapshot.data;
              final isAdmin = userRole == 'admin';

              return AlertDialog(
                title: Text(
                  'Delete Message',
                  style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
                ),
                content: Text(
                  isAdmin
                      ? 'Are you sure you want to delete this message for everyone?'
                      : 'Are you sure you want to delete this message for yourself?',
                  style: GoogleFonts.poppins(),
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text(
                      'Cancel',
                      style: GoogleFonts.poppins(color: Colors.grey[600]),
                    ),
                  ),
                  TextButton(
                    onPressed: () async {
                      Navigator.pop(context);
                      await _deleteMessage(context);
                    },
                    child: Text(
                      'Delete',
                      style: GoogleFonts.poppins(color: Colors.red),
                    ),
                  ),
                ],
              );
            },
          ),
    );
  }

  Future<void> _deleteMessage(BuildContext context) async {
    try {
      final userRole = await SessionService.getUserRole();
      final isAdmin = userRole == 'admin';

      await ChatService.deleteMessage(chatId, message.id);

      if (context.mounted) {
        final successMessage =
            isAdmin
                ? 'Message deleted for everyone'
                : 'Message deleted for you';
        AppSnackbar.showSuccess(context, successMessage);
      }
    } catch (e) {
      if (context.mounted) {
        AppSnackbar.showError(
          context,
          'Failed to delete message: ${e.toString()}',
        );
      }
    }
  }

  void _showMessageInfo(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'Message Info',
              style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildInfoRow('Sender', message.senderName),
                _buildInfoRow('Type', message.type.name.toUpperCase()),
                _buildInfoRow('Time', _formatDateTime(message.timestamp)),
                _buildInfoRow('Status', message.status.name.toUpperCase()),
                if (message.fileName != null)
                  _buildInfoRow('File Name', message.fileName!),
                if (message.fileSize != null)
                  _buildInfoRow(
                    'File Size',
                    _formatFileSize(message.fileSize!),
                  ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  'Close',
                  style: GoogleFonts.poppins(color: const Color(0xFF005368)),
                ),
              ),
            ],
          ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: GoogleFonts.poppins(fontSize: 14, color: Colors.black87),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  // Action handlers
  void _handleShareOutside(BuildContext context) async {
    try {
      // Handle different message types
      if (message.text != null && message.text!.isNotEmpty) {
        // Share text message
        await _shareTextMessage();
      } else if (message.type == MessageType.image &&
          message.mediaUrl != null) {
        // Share image with caption
        await _shareImageMessage();
      } else if (message.type == MessageType.video &&
          message.mediaUrl != null) {
        // Share video with caption
        await _shareVideoMessage();
      } else if (message.type == MessageType.file && message.mediaUrl != null) {
        // Share file
        await _shareFileMessage();
      } else if (message.type == MessageType.audio &&
          message.mediaUrl != null) {
        // Share audio file
        await _shareAudioMessage();
      } else if (message.type == MessageType.order) {
        // Share order information
        await _shareOrderMessage();
      } else if (message.type == MessageType.catalog) {
        // Share catalog information
        await _shareCatalogMessage();
      } else {
        if (context.mounted) {
          AppSnackbar.showInfo(context, 'No content to share');
        }
        return;
      }

      // Show success message (check if widget is still mounted)
      if (context.mounted) {
        AppSnackbar.showSuccess(context, 'Content shared successfully!');
      }
    } catch (e) {
      // Show error message (check if widget is still mounted)
      if (context.mounted) {
        AppSnackbar.showError(
          context,
          'Failed to share content: ${e.toString()}',
        );
      }
    }
  }

  Future<void> _shareTextMessage() async {
    final String shareContent = '''${message.text!}

Shared from Mr. Garments Mobile App
Download: https://play.google.com/store/apps/details?id=com.braincave.mrgarments''';

    await Share.share(shareContent);
  }

  Future<void> _shareImageMessage() async {
    try {
      // Download the image from URL
      final response = await http.get(Uri.parse(message.mediaUrl!));
      if (response.statusCode == 200) {
        // Get temporary directory
        final tempDir = await getTemporaryDirectory();
        final fileName =
            'shared_image_${DateTime.now().millisecondsSinceEpoch}.jpg';
        final file = File('${tempDir.path}/$fileName');

        // Write image data to file
        await file.writeAsBytes(response.bodyBytes);

        // Prepare caption
        String caption = '';
        if (message.text != null && message.text!.isNotEmpty) {
          caption = message.text!;
        }

        final String fullCaption =
            '''${caption.isNotEmpty ? '$caption\n\n' : ''}Shared from Mr. Garments Mobile App
Download: https://play.google.com/store/apps/details?id=com.braincave.mrgarments''';

        // Share image with caption
        await Share.shareXFiles([XFile(file.path)], text: fullCaption);

        // Clean up temporary file after a delay
        Future.delayed(const Duration(seconds: 5), () {
          if (file.existsSync()) {
            file.deleteSync();
          }
        });
      } else {
        // Fallback to text sharing if image download fails
        await _shareTextFallback('Shared an image from Mr. Garments chat');
      }
    } catch (e) {
      // Fallback to text sharing if anything fails
      await _shareTextFallback('Shared an image from Mr. Garments chat');
    }
  }

  Future<void> _shareVideoMessage() async {
    try {
      // Download the video from URL
      final response = await http.get(Uri.parse(message.mediaUrl!));
      if (response.statusCode == 200) {
        // Get temporary directory
        final tempDir = await getTemporaryDirectory();
        final fileName =
            'shared_video_${DateTime.now().millisecondsSinceEpoch}.mp4';
        final file = File('${tempDir.path}/$fileName');

        // Write video data to file
        await file.writeAsBytes(response.bodyBytes);

        // Prepare caption
        String caption = '';
        if (message.text != null && message.text!.isNotEmpty) {
          caption = message.text!;
        }

        final String fullCaption =
            '''${caption.isNotEmpty ? '$caption\n\n' : ''}Shared from Mr. Garments Mobile App
Download: https://play.google.com/store/apps/details?id=com.braincave.mrgarments''';

        // Share video with caption
        await Share.shareXFiles([XFile(file.path)], text: fullCaption);

        // Clean up temporary file after a delay
        Future.delayed(const Duration(seconds: 10), () {
          if (file.existsSync()) {
            file.deleteSync();
          }
        });
      } else {
        // Fallback to text sharing if video download fails
        await _shareTextFallback('Shared a video from Mr. Garments chat');
      }
    } catch (e) {
      // Fallback to text sharing if anything fails
      await _shareTextFallback('Shared a video from Mr. Garments chat');
    }
  }

  Future<void> _shareFileMessage() async {
    try {
      // Download the file from URL
      final response = await http.get(Uri.parse(message.mediaUrl!));
      if (response.statusCode == 200) {
        // Get temporary directory
        final tempDir = await getTemporaryDirectory();
        final fileName =
            message.fileName ??
            'shared_file_${DateTime.now().millisecondsSinceEpoch}';
        final file = File('${tempDir.path}/$fileName');

        // Write file data to file
        await file.writeAsBytes(response.bodyBytes);

        // Prepare caption
        String caption = '';
        if (message.text != null && message.text!.isNotEmpty) {
          caption = message.text!;
        }

        final String fullCaption =
            '''${caption.isNotEmpty ? '$caption\n\n' : ''}Shared from Mr. Garments Mobile App
Download: https://play.google.com/store/apps/details?id=com.braincave.mrgarments''';

        // Share file with caption
        await Share.shareXFiles([XFile(file.path)], text: fullCaption);

        // Clean up temporary file after a delay
        Future.delayed(const Duration(seconds: 10), () {
          if (file.existsSync()) {
            file.deleteSync();
          }
        });
      } else {
        // Fallback to text sharing if file download fails
        await _shareTextFallback(
          'Shared a file: ${message.fileName ?? 'Document'} from Mr. Garments chat',
        );
      }
    } catch (e) {
      // Fallback to text sharing if anything fails
      await _shareTextFallback(
        'Shared a file: ${message.fileName ?? 'Document'} from Mr. Garments chat',
      );
    }
  }

  Future<void> _shareAudioMessage() async {
    try {
      // Download the audio from URL
      final response = await http.get(Uri.parse(message.mediaUrl!));
      if (response.statusCode == 200) {
        // Get temporary directory
        final tempDir = await getTemporaryDirectory();
        final fileName =
            message.fileName ??
            'shared_audio_${DateTime.now().millisecondsSinceEpoch}.mp3';
        final file = File('${tempDir.path}/$fileName');

        // Write audio data to file
        await file.writeAsBytes(response.bodyBytes);

        // Prepare caption
        String caption = '';
        if (message.text != null && message.text!.isNotEmpty) {
          caption = message.text!;
        }

        final String fullCaption =
            '''${caption.isNotEmpty ? '$caption\n\n' : ''}Shared from Mr. Garments Mobile App
Download: https://play.google.com/store/apps/details?id=com.braincave.mrgarments''';

        // Share audio with caption
        await Share.shareXFiles([XFile(file.path)], text: fullCaption);

        // Clean up temporary file after a delay
        Future.delayed(const Duration(seconds: 10), () {
          if (file.existsSync()) {
            file.deleteSync();
          }
        });
      } else {
        // Fallback to text sharing if audio download fails
        await _shareTextFallback(
          'Shared an audio message from Mr. Garments chat',
        );
      }
    } catch (e) {
      // Fallback to text sharing if anything fails
      await _shareTextFallback(
        'Shared an audio message from Mr. Garments chat',
      );
    }
  }

  Future<void> _shareOrderMessage() async {
    String orderInfo = 'Shared an order from Mr. Garments chat';

    // Extract order information from metadata if available
    if (message.metadata != null) {
      final orderData = message.metadata!;
      orderInfo = '''Order Details:
${orderData['orderNumber'] ?? 'Order'} - ${orderData['productName'] ?? 'Product'}
Amount: ${orderData['amount'] ?? 'N/A'}
Status: ${orderData['status'] ?? 'N/A'}''';
    }

    await _shareTextFallback(orderInfo);
  }

  Future<void> _shareCatalogMessage() async {
    String catalogInfo = 'Shared a catalog from Mr. Garments chat';

    // Extract catalog information from metadata if available
    if (message.metadata != null) {
      final catalogData = message.metadata!;
      catalogInfo = '''Catalog Details:
${catalogData['catalogName'] ?? 'Catalog'} - ${catalogData['brandName'] ?? 'Brand'}
Items: ${catalogData['itemCount'] ?? 'Multiple'} products
Category: ${catalogData['category'] ?? 'Fashion'}''';
    }

    await _shareTextFallback(catalogInfo);
  }

  Future<void> _shareTextFallback(String content) async {
    final String shareContent = '''$content

Shared from Mr. Garments Mobile App
Download: https://play.google.com/store/apps/details?id=com.braincave.mrgarments''';

    await Share.share(shareContent);
  }

  void _handleForward(BuildContext context) {
    // Show forward dialog with message selection
    _showForwardDialog(context);
  }

  void _handleCreateOrder(BuildContext context) {
    // Navigate to create order page
    AppSnackbar.showInfo(context, 'Navigating to Create Order page...');
    // Navigate to create order screen
    // Navigator.pushNamed(context, '/create-order');
  }

  void _handleCreateCatalog(BuildContext context) {
    // Show image picker for catalog creation
    _showCatalogCreationDialog(context);
  }

  void _showDeleteOptions(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'Delete Options',
              style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                FutureBuilder<String?>(
                  future: SessionService.getUserRole(),
                  builder: (context, snapshot) {
                    final userRole = snapshot.data;
                    final isAdmin = userRole == 'admin';

                    return ListTile(
                      leading: const Icon(
                        LucideIcons.trash2,
                        color: Colors.red,
                      ),
                      title: Text(
                        'Delete Message',
                        style: GoogleFonts.poppins(),
                      ),
                      subtitle: Text(
                        isAdmin
                            ? 'Delete this message for everyone'
                            : 'Delete this message for me',
                        style: GoogleFonts.poppins(fontSize: 12),
                      ),
                      onTap: () {
                        Navigator.pop(context);
                        _showDeleteConfirmation(context);
                      },
                    );
                  },
                ),
                ListTile(
                  leading: const Icon(
                    LucideIcons.messageSquare,
                    color: Colors.red,
                  ),
                  title: Text('Delete Chat', style: GoogleFonts.poppins()),
                  subtitle: Text(
                    'Delete entire conversation',
                    style: GoogleFonts.poppins(fontSize: 12),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _showDeleteChatConfirmation(context);
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  'Cancel',
                  style: GoogleFonts.poppins(color: Colors.grey[600]),
                ),
              ),
            ],
          ),
    );
  }

  void _showForwardDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => ForwardMessageDialog(message: message, chatId: chatId),
    );
  }

  void _showCatalogCreationDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'Create Catalog',
              style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Select images to create a catalog:',
                  style: GoogleFonts.poppins(),
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildImageSourceButton(
                      context,
                      icon: LucideIcons.camera,
                      label: 'Camera',
                      onTap: () {
                        Navigator.pop(context);
                        AppSnackbar.showInfo(
                          context,
                          'Camera selection coming soon',
                        );
                      },
                    ),
                    _buildImageSourceButton(
                      context,
                      icon: LucideIcons.image,
                      label: 'Gallery',
                      onTap: () {
                        Navigator.pop(context);
                        AppSnackbar.showInfo(
                          context,
                          'Gallery selection coming soon',
                        );
                      },
                    ),
                  ],
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  'Cancel',
                  style: GoogleFonts.poppins(color: Colors.grey[600]),
                ),
              ),
            ],
          ),
    );
  }

  Widget _buildImageSourceButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: const Color(0xFF005368).withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: const Color(0xFF005368).withOpacity(0.3)),
        ),
        child: Column(
          children: [
            Icon(icon, color: const Color(0xFF005368), size: 32),
            const SizedBox(height: 8),
            Text(
              label,
              style: GoogleFonts.poppins(
                color: const Color(0xFF005368),
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteChatConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'Delete Chat',
              style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
            ),
            content: Text(
              'Are you sure you want to delete this entire conversation? This action cannot be undone.',
              style: GoogleFonts.poppins(),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  'Cancel',
                  style: GoogleFonts.poppins(color: Colors.grey[600]),
                ),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.pop(context);
                  await _deleteChat(context);
                },
                child: Text(
                  'Delete',
                  style: GoogleFonts.poppins(color: Colors.red),
                ),
              ),
            ],
          ),
    );
  }

  Future<void> _deleteChat(BuildContext context) async {
    try {
      await ChatService.deleteChat(chatId);

      if (context.mounted) {
        // Navigate back to chat list since the chat is cleared
        Navigator.of(context).popUntil((route) => route.isFirst);
        AppSnackbar.showSuccess(context, 'Chat cleared successfully');
      }
    } catch (e) {
      if (context.mounted) {
        AppSnackbar.showError(
          context,
          'Failed to delete chat: ${e.toString()}',
        );
      }
    }
  }
}

// Forward Message Dialog Widget
class ForwardMessageDialog extends StatefulWidget {
  final Message message;
  final String chatId;

  const ForwardMessageDialog({
    super.key,
    required this.message,
    required this.chatId,
  });

  @override
  State<ForwardMessageDialog> createState() => _ForwardMessageDialogState();
}

class _ForwardMessageDialogState extends State<ForwardMessageDialog> {
  List<Chat> availableChats = [];
  Set<String> selectedChatIds = {};
  bool isLoading = true;
  String? currentUserId;

  @override
  void initState() {
    super.initState();
    _loadChats();
  }

  Future<void> _loadChats() async {
    try {
      final userId = await SessionService.getUserId();
      currentUserId = userId?.toString();
      if (currentUserId == null) {
        if (mounted) {
          Navigator.pop(context);
          AppSnackbar.showError(context, 'User not logged in');
        }
        return;
      }

      // Get user's chats stream and take the first snapshot
      final chatsStream = ChatService.getUserChatsStream(currentUserId!);
      await for (final chats in chatsStream) {
        if (mounted) {
          setState(() {
            // Filter out the current chat to prevent forwarding to same chat
            availableChats =
                chats.where((chat) => chat.id != widget.chatId).toList();
            isLoading = false;
          });
        }
        break; // Take only the first snapshot
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
        AppSnackbar.showError(context, 'Failed to load chats: ${e.toString()}');
      }
    }
  }

  void _toggleChatSelection(String chatId) {
    setState(() {
      if (selectedChatIds.contains(chatId)) {
        selectedChatIds.remove(chatId);
      } else {
        selectedChatIds.add(chatId);
      }
    });
  }

  Future<void> _forwardMessage() async {
    if (selectedChatIds.isEmpty) {
      AppSnackbar.showInfo(context, 'Please select at least one chat');
      return;
    }

    try {
      await ChatService.forwardMessage(
        fromChatId: widget.chatId,
        messageId: widget.message.id,
        toChatIds: selectedChatIds.toList(),
      );

      if (mounted) {
        Navigator.pop(context);
        AppSnackbar.showSuccess(
          context,
          'Message forwarded to ${selectedChatIds.length} chat${selectedChatIds.length > 1 ? 's' : ''}',
        );
      }
    } catch (e) {
      if (mounted) {
        AppSnackbar.showError(
          context,
          'Failed to forward message: ${e.toString()}',
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Forward Message',
                  style: GoogleFonts.poppins(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF005368),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(
                    LucideIcons.x,
                    color: Color(0xFF005368),
                    size: 20,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Message preview
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Row(
                children: [
                  Icon(
                    _getMessageIcon(widget.message.type),
                    color: const Color(0xFF005368),
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _getMessagePreview(widget.message),
                      style: GoogleFonts.poppins(fontSize: 14),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            Text(
              'Select chats to forward to:',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),

            const SizedBox(height: 12),

            // Chat list
            Expanded(
              child:
                  isLoading
                      ? const Center(child: CircularProgressIndicator())
                      : availableChats.isEmpty
                      ? Center(
                        child: Text(
                          'No chats available',
                          style: GoogleFonts.poppins(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                      )
                      : ListView.builder(
                        itemCount: availableChats.length,
                        itemBuilder: (context, index) {
                          final chat = availableChats[index];
                          final isSelected = selectedChatIds.contains(chat.id);

                          return _buildChatItem(chat, isSelected);
                        },
                      ),
            ),

            const SizedBox(height: 16),

            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text(
                    'Cancel',
                    style: GoogleFonts.poppins(color: Colors.grey[600]),
                  ),
                ),
                const SizedBox(width: 12),
                ElevatedButton(
                  onPressed:
                      selectedChatIds.isNotEmpty ? _forwardMessage : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF005368),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(
                    'Forward (${selectedChatIds.length})',
                    style: GoogleFonts.poppins(fontWeight: FontWeight.w500),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChatItem(Chat chat, bool isSelected) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isSelected ? const Color(0xFF005368) : Colors.grey[300]!,
          width: isSelected ? 2 : 1,
        ),
        color:
            isSelected
                ? const Color(0xFF005368).withOpacity(0.1)
                : Colors.white,
      ),
      child: ListTile(
        onTap: () => _toggleChatSelection(chat.id),
        leading: CircleAvatar(
          backgroundColor: const Color(0xFF005368),
          child:
              chat.type == ChatType.group
                  ? const Icon(LucideIcons.users, color: Colors.white, size: 20)
                  : const Icon(LucideIcons.user, color: Colors.white, size: 20),
        ),
        title: Text(
          _getChatDisplayName(chat),
          style: GoogleFonts.poppins(fontWeight: FontWeight.w500, fontSize: 14),
        ),
        subtitle:
            chat.lastMessage != null
                ? Text(
                  _getMessagePreview(chat.lastMessage!),
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                )
                : null,
        trailing:
            isSelected
                ? const Icon(
                  LucideIcons.check,
                  color: Color(0xFF005368),
                  size: 20,
                )
                : null,
      ),
    );
  }

  String _getChatDisplayName(Chat chat) {
    if (chat.type == ChatType.group) {
      return chat.groupName ?? 'Group Chat';
    } else {
      // For individual chats, show the other person's name
      final otherMemberId = chat.memberIds.firstWhere(
        (id) => id != currentUserId,
        orElse: () => chat.memberIds.first,
      );
      return chat.memberNames[otherMemberId] ?? 'Unknown User';
    }
  }

  IconData _getMessageIcon(MessageType type) {
    switch (type) {
      case MessageType.text:
        return LucideIcons.messageSquare;
      case MessageType.image:
        return LucideIcons.image;
      case MessageType.video:
        return LucideIcons.video;
      case MessageType.audio:
        return LucideIcons.mic;
      case MessageType.file:
        return LucideIcons.file;
      case MessageType.location:
        return LucideIcons.mapPin;
      case MessageType.contact:
        return LucideIcons.user;
      case MessageType.order:
        return LucideIcons.shoppingBag;
      case MessageType.catalog:
        return LucideIcons.bookOpen;
    }
  }

  String _getMessagePreview(Message message) {
    switch (message.type) {
      case MessageType.text:
        return message.text ?? 'Text message';
      case MessageType.image:
        return '📷 Image';
      case MessageType.video:
        return '🎥 Video';
      case MessageType.audio:
        return '🎵 Audio';
      case MessageType.file:
        return '📄 ${message.fileName ?? 'File'}';
      case MessageType.location:
        return '📍 Location';
      case MessageType.contact:
        return '👤 Contact';
      case MessageType.order:
        return '🛍️ Order';
      case MessageType.catalog:
        return '📚 Catalog';
    }
  }
}
