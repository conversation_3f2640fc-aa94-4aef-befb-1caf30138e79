import 'dart:io';
import 'dart:ui';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mr_garments_mobile/models/chat_user.dart';
import 'package:mr_garments_mobile/models/chat.dart';
import 'package:mr_garments_mobile/models/message.dart';
import 'package:mr_garments_mobile/models/group.dart';
import 'package:mr_garments_mobile/services/chat_service.dart';
import 'package:mr_garments_mobile/services/session_service.dart';
import 'package:mr_garments_mobile/utils/enhanced_image_cache.dart';

// ==================== CHAT STATE CLASSES ====================

class ChatState {
  final AsyncValue<List<Chat>> chats;
  final AsyncValue<List<ChatUser>> users;
  final AsyncValue<List<Group>> groups;
  final bool isLoading;
  final String? error;

  const ChatState({
    this.chats = const AsyncValue.loading(),
    this.users = const AsyncValue.loading(),
    this.groups = const AsyncValue.loading(),
    this.isLoading = false,
    this.error,
  });

  ChatState copyWith({
    AsyncValue<List<Chat>>? chats,
    AsyncValue<List<ChatUser>>? users,
    AsyncValue<List<Group>>? groups,
    bool? isLoading,
    String? error,
  }) {
    return ChatState(
      chats: chats ?? this.chats,
      users: users ?? this.users,
      groups: groups ?? this.groups,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }
}

class MessageState {
  final AsyncValue<List<Message>> messages;
  final bool isLoading;
  final String? error;
  final Message? replyToMessage;

  const MessageState({
    this.messages = const AsyncValue.loading(),
    this.isLoading = false,
    this.error,
    this.replyToMessage,
  });

  MessageState copyWith({
    AsyncValue<List<Message>>? messages,
    bool? isLoading,
    String? error,
    Message? replyToMessage,
  }) {
    return MessageState(
      messages: messages ?? this.messages,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      replyToMessage: replyToMessage,
    );
  }
}

// ==================== CHAT NOTIFIER ====================

class ChatNotifier extends StateNotifier<ChatState> {
  ChatNotifier() : super(const ChatState()) {
    _initializeChat();
  }

  Future<void> _initializeChat() async {
    try {
      final currentUserId = await SessionService.getUserId();
      if (currentUserId != null) {
        await loadChats();
        await loadUsers();
        await loadGroups();
        await ChatService.initializeFCM();
      }
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// Load user's chats
  Future<void> loadChats() async {
    try {
      final currentUserId = await SessionService.getUserId();
      if (currentUserId == null) return;

      state = state.copyWith(isLoading: true);

      // This will be handled by the stream provider
      // Just update loading state here
      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// Load all users
  Future<void> loadUsers() async {
    try {
      state = state.copyWith(isLoading: true);
      final users = await ChatService.getAllUsers();
      state = state.copyWith(users: AsyncValue.data(users), isLoading: false);
    } catch (e) {
      state = state.copyWith(
        users: AsyncValue.error(e, StackTrace.current),
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Load user's groups
  Future<void> loadGroups() async {
    try {
      final currentUserId = await SessionService.getUserId();
      if (currentUserId == null) return;

      state = state.copyWith(isLoading: true);
      final groups = await ChatService.getUserGroups(currentUserId.toString());
      state = state.copyWith(groups: AsyncValue.data(groups), isLoading: false);
    } catch (e) {
      state = state.copyWith(
        groups: AsyncValue.error(e, StackTrace.current),
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Create individual chat
  Future<String?> createIndividualChat(String otherUserId) async {
    try {
      state = state.copyWith(isLoading: true);
      final chatId = await ChatService.createIndividualChat(otherUserId);
      state = state.copyWith(isLoading: false);
      await loadChats(); // Refresh chats
      return chatId;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return null;
    }
  }

  /// Create group
  Future<String?> createGroup({
    required String groupName,
    String? description,
    required List<String> memberIds,
    File? groupImage,
  }) async {
    try {
      state = state.copyWith(isLoading: true);
      final groupId = await ChatService.createGroup(
        groupName: groupName,
        description: description,
        memberIds: memberIds,
        groupImage: groupImage,
      );
      state = state.copyWith(isLoading: false);
      await loadChats(); // Refresh chats
      await loadGroups(); // Refresh groups
      return groupId;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return null;
    }
  }

  /// Search users
  Future<List<ChatUser>> searchUsers(String query) async {
    try {
      return await ChatService.searchUsers(query);
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return [];
    }
  }

  /// Add member to group
  Future<bool> addMemberToGroup(String groupId, String userId) async {
    try {
      state = state.copyWith(isLoading: true);
      await ChatService.addMemberToGroup(groupId, userId);
      state = state.copyWith(isLoading: false);
      await loadGroups(); // Refresh groups
      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return false;
    }
  }

  /// Remove member from group
  Future<bool> removeMemberFromGroup(String groupId, String userId) async {
    try {
      state = state.copyWith(isLoading: true);
      await ChatService.removeMemberFromGroup(groupId, userId);
      state = state.copyWith(isLoading: false);
      await loadGroups(); // Refresh groups
      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return false;
    }
  }

  /// Clear chat messages
  Future<bool> clearChat(String chatId) async {
    try {
      state = state.copyWith(isLoading: true);
      await ChatService.clearChat(chatId);
      state = state.copyWith(isLoading: false);
      await loadChats(); // Refresh chats
      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return false;
    }
  }

  /// Delete chat
  Future<bool> deleteChat(String chatId) async {
    try {
      state = state.copyWith(isLoading: true);
      await ChatService.deleteChat(chatId);
      state = state.copyWith(isLoading: false);
      await loadChats(); // Refresh chats
      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return false;
    }
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

// ==================== MESSAGE NOTIFIER ====================

class MessageNotifier extends StateNotifier<MessageState> {
  final String chatId;

  MessageNotifier(this.chatId) : super(const MessageState());

  String _getMessageDisplayText(Message message) {
    // First check if we have text content regardless of type
    if (message.text != null && message.text!.isNotEmpty) {
      return message.text!;
    }

    // Then handle specific media types
    switch (message.type) {
      case MessageType.image:
        return '📷 Image';
      case MessageType.file:
        return '📎 ${message.fileName ?? 'File'}';
      case MessageType.audio:
        return '🎵 Audio';
      case MessageType.video:
        return '🎥 Video';
      case MessageType.location:
        return '📍 Location';
      case MessageType.contact:
        return '👤 Contact';
      case MessageType.catalog:
        return '📋 Catalog';
      case MessageType.text:
      default:
        // Fallback - this should rarely be reached now
        return message.text ?? 'Message';
    }
  }

  /// Send text message
  Future<bool> sendTextMessage(String text, {String? replyToMessageId}) async {
    try {
      state = state.copyWith(isLoading: true);

      // Get reply information from current state
      String? replyToText;
      String? replyToSenderName;
      if (state.replyToMessage != null) {
        replyToText = _getMessageDisplayText(state.replyToMessage!);
        replyToSenderName = state.replyToMessage!.senderName;
      }

      await ChatService.sendTextMessage(
        chatId: chatId,
        text: text,
        replyToMessageId: replyToMessageId,
        replyToText: replyToText,
        replyToSenderName: replyToSenderName,
      );
      state = state.copyWith(isLoading: false, replyToMessage: null);
      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return false;
    }
  }

  /// Send image message
  Future<bool> sendImageMessage(
    File imageFile, {
    String? replyToMessageId,
  }) async {
    try {
      state = state.copyWith(isLoading: true);

      // Get reply information from current state
      String? replyToText;
      String? replyToSenderName;
      if (state.replyToMessage != null) {
        replyToText = _getMessageDisplayText(state.replyToMessage!);
        replyToSenderName = state.replyToMessage!.senderName;
      }

      await ChatService.sendImageMessage(
        chatId: chatId,
        imageFile: imageFile,
        replyToMessageId: replyToMessageId,
        replyToText: replyToText,
        replyToSenderName: replyToSenderName,
      );
      state = state.copyWith(isLoading: false, replyToMessage: null);
      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return false;
    }
  }

  /// Send file message
  Future<bool> sendFileMessage(File file, {String? replyToMessageId}) async {
    try {
      state = state.copyWith(isLoading: true);

      // Get reply information from current state
      String? replyToText;
      String? replyToSenderName;
      if (state.replyToMessage != null) {
        replyToText = _getMessageDisplayText(state.replyToMessage!);
        replyToSenderName = state.replyToMessage!.senderName;
      }

      await ChatService.sendFileMessage(
        chatId: chatId,
        file: file,
        replyToMessageId: replyToMessageId,
        replyToText: replyToText,
        replyToSenderName: replyToSenderName,
      );
      state = state.copyWith(isLoading: false, replyToMessage: null);
      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return false;
    }
  }

  /// Send single catalog message
  Future<bool> sendSingleCatalogMessage(
    Map<String, dynamic> catalog, {
    String? replyToMessageId,
  }) async {
    try {
      state = state.copyWith(isLoading: true);

      // Get reply information from current state
      String? replyToText;
      String? replyToSenderName;
      if (state.replyToMessage != null) {
        replyToText = _getMessageDisplayText(state.replyToMessage!);
        replyToSenderName = state.replyToMessage!.senderName;
      }

      await ChatService.sendSingleCatalogMessage(
        chatId: chatId,
        catalog: catalog,
        replyToMessageId: replyToMessageId,
        replyToText: replyToText,
        replyToSenderName: replyToSenderName,
      );
      state = state.copyWith(isLoading: false, replyToMessage: null);
      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return false;
    }
  }

  /// Send catalog message (multiple catalogs - kept for backward compatibility)
  Future<bool> sendCatalogMessage(
    List<Map<String, dynamic>> catalogs, {
    String? replyToMessageId,
  }) async {
    try {
      state = state.copyWith(isLoading: true);

      // Get reply information from current state
      String? replyToText;
      String? replyToSenderName;
      if (state.replyToMessage != null) {
        replyToText = _getMessageDisplayText(state.replyToMessage!);
        replyToSenderName = state.replyToMessage!.senderName;
      }

      await ChatService.sendCatalogMessage(
        chatId: chatId,
        catalogs: catalogs,
        replyToMessageId: replyToMessageId,
        replyToText: replyToText,
        replyToSenderName: replyToSenderName,
      );
      state = state.copyWith(isLoading: false, replyToMessage: null);
      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return false;
    }
  }

  /// Set reply to message
  void setReplyToMessage(Message? message) {
    state = state.copyWith(replyToMessage: message);
  }

  /// Clear reply
  void clearReply() {
    state = state.copyWith(replyToMessage: null);
  }

  /// Preload images from messages for better performance with enhanced caching
  Future<void> preloadImagesFromMessages(List<Message> messages) async {
    final imageUrls =
        messages
            .where(
              (message) =>
                  message.type == MessageType.image &&
                  message.mediaUrl != null &&
                  message.mediaUrl!.isNotEmpty,
            )
            .map((message) => message.mediaUrl!)
            .toList();

    if (imageUrls.isNotEmpty) {
      // Preload images in background with enhanced caching
      EnhancedImageCache.preloadImages(
        imageUrls,
        onProgress: (loaded, total) {
          // Optional: Could emit progress updates if needed
          print('Preloaded $loaded/$total images for chat $chatId');
        },
        onComplete: () {
          print('All images preloaded for chat $chatId');
        },
      );
    }
  }

  /// Preload images immediately when messages are loaded
  void _preloadImagesFromNewMessages(List<Message> messages) {
    // Run preloading in background without awaiting
    Future.microtask(() => preloadImagesFromMessages(messages));
  }

  /// Mark message as read
  Future<void> markMessageAsRead(String messageId) async {
    try {
      final currentUserId = await SessionService.getUserId();
      if (currentUserId != null) {
        await ChatService.markMessageAsRead(
          chatId,
          messageId,
          currentUserId.toString(),
        );
      }
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// Mark all messages as read
  Future<void> markAllMessagesAsRead(String userId) async {
    try {
      await ChatService.markAllMessagesAsRead(chatId, userId);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// Delete message
  Future<bool> deleteMessage(String messageId) async {
    try {
      await ChatService.deleteMessage(chatId, messageId);
      return true;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  /// Forward message
  Future<bool> forwardMessage(String messageId, List<String> toChatIds) async {
    try {
      state = state.copyWith(isLoading: true);
      await ChatService.forwardMessage(
        fromChatId: chatId,
        messageId: messageId,
        toChatIds: toChatIds,
      );
      state = state.copyWith(isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return false;
    }
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

// ==================== PROVIDERS ====================

/// Main chat provider
final chatProvider = StateNotifierProvider<ChatNotifier, ChatState>(
  (ref) => ChatNotifier(),
);

/// Message provider for specific chat
final messageProvider =
    StateNotifierProvider.family<MessageNotifier, MessageState, String>(
      (ref, chatId) => MessageNotifier(chatId),
    );

// ==================== SESSION PROVIDERS ====================

/// Provider for current user ID that refreshes when user changes
final currentUserIdProvider = StreamProvider<String?>((ref) async* {
  String? lastUserId;

  while (true) {
    final currentUserId = await SessionService.getUserId();
    final currentUserIdStr = currentUserId?.toString();

    if (currentUserIdStr != lastUserId) {
      lastUserId = currentUserIdStr;
      yield currentUserIdStr;
    }

    // Check every 1 second for user changes
    await Future.delayed(const Duration(seconds: 1));
  }
});

/// Stream provider for user's chats (all chats)
final userChatsStreamProvider = StreamProvider<List<Chat>>((ref) async* {
  final currentUserId = await SessionService.getUserId();
  if (currentUserId != null) {
    yield* ChatService.getUserChatsStream(currentUserId.toString());
  } else {
    yield [];
  }
});

/// Stream provider for user's individual chats only
final individualChatsStreamProvider = StreamProvider.autoDispose<List<Chat>>((
  ref,
) async* {
  // Get current user ID and refresh when it changes
  final currentUserId = await SessionService.getUserId();

  if (currentUserId != null) {
    yield* ChatService.getUserChatsStream(currentUserId.toString()).map(
      (chats) =>
          chats.where((chat) => chat.type == ChatType.individual).toList(),
    );
  } else {
    yield [];
  }
});

/// Stream provider for user's group chats only
final groupChatsStreamProvider = StreamProvider<List<Chat>>((ref) async* {
  final currentUserId = await SessionService.getUserId();
  if (currentUserId != null) {
    yield* ChatService.getUserChatsStream(currentUserId.toString()).map(
      (chats) => chats.where((chat) => chat.type == ChatType.group).toList(),
    );
  } else {
    yield [];
  }
});

/// Enhanced stream provider for messages with automatic image preloading and user-specific filtering
final messagesStreamProvider = StreamProvider.family
    .autoDispose<List<Message>, String>((ref, chatId) async* {
      // Get current user ID to filter deleted messages
      final currentUserId = await SessionService.getUserId();
      final currentUserIdString = currentUserId?.toString();

      await for (final messages in ChatService.getMessagesStream(chatId)) {
        // Filter out messages that are deleted by the current user
        final filteredMessages =
            currentUserIdString != null
                ? messages
                    .where(
                      (message) => !message.isDeletedBy(currentUserIdString),
                    )
                    .toList()
                : messages;

        // Preload images from new messages in background
        final messageNotifier = ref.read(messageProvider(chatId).notifier);
        messageNotifier._preloadImagesFromNewMessages(filteredMessages);

        yield filteredMessages;
      }
    });

/// Provider for current user
final currentUserProvider = FutureProvider<ChatUser?>((ref) async {
  final currentUserId = await SessionService.getUserId();
  if (currentUserId != null) {
    return await ChatService.getUser(currentUserId.toString());
  }
  return null;
});

/// Provider for other user (for online status) - Real-time stream
final otherUserProvider = StreamProvider.family.autoDispose<ChatUser?, String>((
  ref,
  userId,
) {
  return ChatService.getUserStream(userId);
});

/// Provider for specific chat
final chatDetailProvider = FutureProvider.family<Chat?, String>((
  ref,
  chatId,
) async {
  return await ChatService.getChat(chatId);
});

/// Provider for specific group
final groupProvider = FutureProvider.family<Group?, String>((
  ref,
  groupId,
) async {
  return await ChatService.getGroup(groupId);
});

/// Provider for all users (for member selection)
final allUsersProvider = FutureProvider<List<ChatUser>>((ref) async {
  return await ChatService.getAllUsers();
});

/// Provider for user search results
final userSearchProvider = StateProvider<List<ChatUser>>((ref) => []);

/// Provider for selected users (for group creation)
final selectedUsersProvider = StateProvider<List<ChatUser>>((ref) => []);

/// Provider for selected messages (for forwarding/deleting)
final selectedMessagesProvider = StateProvider<List<Message>>((ref) => []);

/// Provider for unread message count
final unreadMessageCountProvider = FutureProvider<int>((ref) async {
  final currentUserId = await SessionService.getUserId();
  if (currentUserId != null) {
    return await ChatService.getUnreadMessageCount(currentUserId.toString());
  }
  return 0;
});

/// Provider for chat typing status (userId -> isTyping), real-time from Firestore
final typingStatusProvider = StreamProvider.family<Map<String, bool>, String>((
  ref,
  chatId,
) {
  return ChatService.getTypingStatusStream(chatId);
});

/// Provider for online users
final onlineUsersProvider = StateProvider<Set<String>>((ref) => {});

/// Provider for message selection mode
final messageSelectionModeProvider = StateProvider<bool>((ref) => false);

/// Provider for chat search query
final chatSearchQueryProvider = StateProvider<String>((ref) => '');

/// Provider for filtered individual chats based on search
final filteredChatsProvider = Provider<AsyncValue<List<Chat>>>((ref) {
  final chatsAsync = ref.watch(individualChatsStreamProvider);
  final searchQuery = ref.watch(chatSearchQueryProvider);

  return chatsAsync.when(
    data: (chats) {
      if (searchQuery.isEmpty) {
        return AsyncValue.data(chats);
      }

      final filteredChats =
          chats.where((chat) {
            // Filter by chat name or last message
            final chatName = chat.getDisplayName('').toLowerCase();
            final lastMessageText = chat.lastMessage?.text?.toLowerCase() ?? '';
            final query = searchQuery.toLowerCase();

            return chatName.contains(query) || lastMessageText.contains(query);
          }).toList();

      return AsyncValue.data(filteredChats);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
  );
});

/// Provider for filtered group chats based on search
final filteredGroupChatsProvider = Provider<AsyncValue<List<Chat>>>((ref) {
  final chatsAsync = ref.watch(groupChatsStreamProvider);
  final searchQuery = ref.watch(chatSearchQueryProvider);

  return chatsAsync.when(
    data: (chats) {
      if (searchQuery.isEmpty) {
        return AsyncValue.data(chats);
      }

      final filteredChats =
          chats.where((chat) {
            // Filter by group name or last message
            final groupName = (chat.groupName ?? '').toLowerCase();
            final lastMessageText = chat.lastMessage?.text?.toLowerCase() ?? '';
            final query = searchQuery.toLowerCase();

            return groupName.contains(query) || lastMessageText.contains(query);
          }).toList();

      return AsyncValue.data(filteredChats);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
  );
});

// ==================== UTILITY PROVIDERS ====================

/// Provider for formatting message time
final messageTimeProvider = Provider.family<String, DateTime>((ref, timestamp) {
  final now = DateTime.now();
  final difference = now.difference(timestamp);

  if (difference.inDays > 0) {
    return '${difference.inDays}d ago';
  } else if (difference.inHours > 0) {
    return '${difference.inHours}h ago';
  } else if (difference.inMinutes > 0) {
    return '${difference.inMinutes}m ago';
  } else {
    return 'Just now';
  }
});

/// Provider for formatting file size
final fileSizeProvider = Provider.family<String, int>((ref, bytes) {
  if (bytes < 1024) {
    return '$bytes B';
  } else if (bytes < 1024 * 1024) {
    return '${(bytes / 1024).toStringAsFixed(1)} KB';
  } else if (bytes < 1024 * 1024 * 1024) {
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  } else {
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
});

/// Provider for image cache information (for debugging)
final imageCacheInfoProvider = FutureProvider<Map<String, dynamic>>((
  ref,
) async {
  return await EnhancedImageCache.getCacheInfo();
});

/// Provider for clearing image cache
final clearImageCacheProvider = Provider<VoidCallback>((ref) {
  return () {
    EnhancedImageCache.clearMemoryCache();
  };
});
