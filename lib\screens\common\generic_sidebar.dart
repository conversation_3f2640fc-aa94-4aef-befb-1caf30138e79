import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:mr_garments_mobile/screens/Auth/login_screen.dart';
import 'package:mr_garments_mobile/screens/common/edit_profile_screen.dart';
import 'package:mr_garments_mobile/screens/common/generic_staff_management_screen.dart';
import 'package:mr_garments_mobile/screens/common/generic_retailer_management_screen.dart';
import 'package:mr_garments_mobile/services/auth_service.dart';
import 'package:mr_garments_mobile/services/session_service.dart';
// import 'package:mr_garments_mobile/utils/notification_test_helper.dart';
import 'package:mr_garments_mobile/utils/snackbar.dart';

class GenericSidebar extends StatefulWidget {
  final String userType; // 'admin', 'manufacturer', 'retailer', 'distributor'
  final List<SidebarMenuItem>? customMenuItems;

  const GenericSidebar({
    super.key,
    required this.userType,
    this.customMenuItems,
  });

  @override
  State<GenericSidebar> createState() => _GenericSidebarState();
}

class _GenericSidebarState extends State<GenericSidebar> {
  String _userName = 'User';
  String _userEmail = '<EMAIL>';
  String? _profileImageUrl;
  bool _isLoading = true;
  bool _isStaffUser = false;

  @override
  /// Initializes the state of the sidebar widget and loads user data.
  void initState() {
    super.initState();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    try {
      final userData = await SessionService.getUserData();
      final userRole = await SessionService.getUserRole();
      if (userData != null) {
        setState(() {
          _userName = userData['name'] ?? 'User';
          _userEmail = userData['email'] ?? '<EMAIL>';
          _profileImageUrl = userData['profile_image_url'];
          _isStaffUser = userRole == 'staff';
          _isLoading = false;
        });
      } else {
        setState(() => _isLoading = false);
      }
    } catch (e) {
      setState(() => _isLoading = false);
    }
  }

  void _showAboutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'About Mr. Garments',
            style: GoogleFonts.poppins(
              fontWeight: FontWeight.w600,
              fontSize: 21,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Version: 1.0.0', style: GoogleFonts.poppins()),
              const SizedBox(height: 8),
              Text('Build: 1.0.0+1', style: GoogleFonts.poppins()),
              const SizedBox(height: 8),
              Text('Developed by BrainCave Soft', style: GoogleFonts.poppins()),
              const SizedBox(height: 16),
              Text(
                'A comprehensive garment management solution for manufacturers, retailers, and distributors.',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'OK',
                style: GoogleFonts.poppins(color: const Color(0xFF005368)),
              ),
            ),
          ],
        );
      },
    );
  }

  List<Widget> _buildMenuItems() {
    List<Widget> items = [];

    // Add custom menu items if provided
    if (widget.customMenuItems != null) {
      for (var item in widget.customMenuItems!) {
        items.add(
          ListTile(
            leading: Icon(item.icon),
            title: Text(item.title, style: GoogleFonts.poppins()),
            subtitle:
                item.subtitle != null
                    ? Text(
                      item.subtitle!,
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    )
                    : null,
            onTap: item.onTap,
          ),
        );
      }
      items.add(const Divider());
    }

    // Add staff management for non-admin users who are not staff themselves
    if (widget.userType != 'admin' && !_isStaffUser) {
      items.add(
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            "Management",
            style: GoogleFonts.poppins(
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
        ),
      );
      items.add(
        ListTile(
          leading: const Icon(LucideIcons.users),
          title: Text("Manage Staff", style: GoogleFonts.poppins()),
          subtitle: Text(
            "Add and manage staff members",
            style: GoogleFonts.poppins(fontSize: 12, color: Colors.grey[600]),
          ),
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder:
                    (_) => GenericStaffManagementScreen(
                      companyType: widget.userType,
                    ),
              ),
            );
          },
        ),
      );

      // Add retailer management for distributors only
      if (widget.userType == 'distributor') {
        items.add(
          ListTile(
            leading: const Icon(LucideIcons.store),
            title: Text("Manage Retailers", style: GoogleFonts.poppins()),
            subtitle: Text(
              "Add and manage retailers",
              style: GoogleFonts.poppins(fontSize: 12, color: Colors.grey[600]),
            ),
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder:
                      (_) => GenericRetailerManagementScreen(
                        companyType: widget.userType,
                      ),
                ),
              );
            },
          ),
        );
      }

      items.add(const Divider());
    }

    return items;
  }

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            UserAccountsDrawerHeader(
              decoration: const BoxDecoration(color: Color(0xFF00536B)),
              accountName: Text(
                _userName,
                style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
              ),
              accountEmail: Text(_userEmail, style: GoogleFonts.poppins()),
              currentAccountPicture: CircleAvatar(
                radius: 25,
                backgroundColor: Colors.white,
                backgroundImage:
                    _profileImageUrl != null
                        ? NetworkImage(_profileImageUrl!)
                        : null,
                child:
                    _profileImageUrl == null
                        ? Text(
                          _userName.isNotEmpty
                              ? _userName[0].toUpperCase()
                              : 'U',
                          style: GoogleFonts.poppins(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF005368),
                          ),
                        )
                        : null,
              ),
            ),

            // Scrollable content
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Custom menu items
                    ..._buildMenuItems(),

                    // Profile Section
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      child: Text(
                        "Profile & Settings",
                        style: GoogleFonts.poppins(
                          fontWeight: FontWeight.bold,
                          color: Colors.grey[600],
                        ),
                      ),
                    ),
                    ListTile(
                      leading: const Icon(LucideIcons.user),
                      title: Text("Edit Profile", style: GoogleFonts.poppins()),
                      subtitle: Text(
                        "Update profile, change password",
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const EditProfileScreen(),
                          ),
                        );
                      },
                    ),
                    ListTile(
                      leading: const Icon(LucideIcons.settings),
                      title: Text("App Settings", style: GoogleFonts.poppins()),
                      subtitle: Text(
                        "Notifications, preferences",
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                      onTap: () {
                        AppSnackbar.showInfo(
                          context,
                          'App settings feature coming soon',
                        );
                      },
                    ),
                    // ListTile(
                    //   leading: const Icon(LucideIcons.settings),
                    //   title: Text("App1 Settings", style: GoogleFonts.poppins()),
                    //   subtitle: Text(
                    //     "Notifications, preferences",
                    //     style: GoogleFonts.poppins(
                    //       fontSize: 12,
                    //       color: Colors.grey[600],
                    //     ),
                    //   ),
                    //   onTap: () {
                    //     Navigator.push(
                    //       context,
                    //       MaterialPageRoute(
                    //         builder:
                    //             (context) =>
                    //                 NotificationTestHelper.createTestWidget(),
                    //       ),
                    //     );
                    //   },
                    // ),

                    const Divider(),

                    // Support Section
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      child: Text(
                        "Support & Information",
                        style: GoogleFonts.poppins(
                          fontWeight: FontWeight.bold,
                          color: Colors.grey[600],
                        ),
                      ),
                    ),
                    ListTile(
                      leading: const Icon(LucideIcons.helpCircle),
                      title: Text(
                        "Help & Support",
                        style: GoogleFonts.poppins(),
                      ),
                      subtitle: Text(
                        "Get help and contact support",
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                      onTap: () {
                        AppSnackbar.showInfo(
                          context,
                          'Help & support feature coming soon',
                        );
                      },
                    ),
                    ListTile(
                      leading: const Icon(LucideIcons.book),
                      title: Text(
                        "Terms & Conditions",
                        style: GoogleFonts.poppins(),
                      ),
                      onTap: () {
                        AppSnackbar.showInfo(
                          context,
                          'Terms & conditions feature coming soon',
                        );
                      },
                    ),
                    ListTile(
                      leading: const Icon(LucideIcons.shield),
                      title: Text(
                        "Privacy Policy",
                        style: GoogleFonts.poppins(),
                      ),
                      onTap: () {
                        AppSnackbar.showInfo(
                          context,
                          'Privacy policy feature coming soon',
                        );
                      },
                    ),
                    ListTile(
                      leading: const Icon(LucideIcons.info),
                      title: Text("About App", style: GoogleFonts.poppins()),
                      subtitle: Text(
                        "Version, build info",
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                      onTap: () {
                        _showAboutDialog(context);
                      },
                    ),
                    const SizedBox(height: 20),

                    // Logout
                    ListTile(
                      leading: const Icon(LucideIcons.logOut),
                      title: Text("Log out", style: GoogleFonts.poppins()),
                      onTap: () async {
                        final confirm = await showDialog<bool>(
                          context: context,
                          builder:
                              (_) => AlertDialog(
                                title: const Text('Confirm Logout'),
                                content: const Padding(
                                  padding: EdgeInsets.symmetric(vertical: 6),
                                  child: Text(
                                    'Are you sure you want to log out?',
                                  ),
                                ),
                                actions: [
                                  Row(
                                    children: [
                                      Expanded(
                                        child: ElevatedButton(
                                          onPressed:
                                              () =>
                                                  Navigator.pop(context, false),
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: Colors.grey[300],
                                            foregroundColor: Colors.black,
                                            padding: const EdgeInsets.symmetric(
                                              vertical: 12,
                                            ),
                                          ),
                                          child: const Text('Cancel'),
                                        ),
                                      ),
                                      const SizedBox(width: 12),
                                      Expanded(
                                        child: ElevatedButton(
                                          onPressed:
                                              () =>
                                                  Navigator.pop(context, true),
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: const Color(
                                              0xFF00536B,
                                            ),
                                            foregroundColor: Colors.white,
                                            padding: const EdgeInsets.symmetric(
                                              vertical: 12,
                                            ),
                                          ),
                                          child: const Text('Log out'),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                        );
                        if (confirm != true) {
                          return;
                        }
                        try {
                          await AuthService.logoutWithSession();
                          if (context.mounted) {
                            Navigator.of(context).pushReplacement(
                              MaterialPageRoute(
                                builder: (_) => const LoginScreen(),
                              ),
                            );
                          }
                        } catch (e) {
                          if (!context.mounted) return;
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(content: Text('Logout failed: $e')),
                          );
                        }
                      },
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class SidebarMenuItem {
  final IconData icon;
  final String title;
  final String? subtitle;
  final VoidCallback onTap;

  SidebarMenuItem({
    required this.icon,
    required this.title,
    this.subtitle,
    required this.onTap,
  });
}
