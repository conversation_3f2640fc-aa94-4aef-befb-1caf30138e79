import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:mr_garments_mobile/models/message.dart';

class MessageUtils {
  /// Groups messages that belong to the same image group
  /// Returns a list of messages that are part of the same image group as the target message
  static Future<List<Message>> getImageGroupMessages({
    required String chatId,
    required Message targetMessage,
    required CollectionReference messagesCollection,
  }) async {
    if (targetMessage.type != MessageType.image) {
      return [targetMessage];
    }

    final List<Message> groupMessages = [];
    final timestamp = targetMessage.timestamp;

    try {
      // Query messages before target message within 1 minute window
      final beforeMessages =
          await messagesCollection
              .where('senderId', isEqualTo: targetMessage.senderId)
              .where('type', isEqualTo: 'image')
              .where('timestamp', isLessThanOrEqualTo: timestamp)
              .orderBy('timestamp', descending: true)
              .get();

      // Query messages after target message within 1 minute window
      final afterMessages =
          await messagesCollection
              .where('senderId', isEqualTo: targetMessage.senderId)
              .where('type', isEqualTo: 'image')
              .where('timestamp', isGreaterThan: timestamp)
              .orderBy('timestamp')
              .get();

      // Process messages before target message
      for (var doc in beforeMessages.docs) {
        final message = Message.fromDocument(doc);
        if (timestamp.difference(message.timestamp).inMinutes.abs() <= 1) {
          groupMessages.add(message);
        } else {
          break; // Stop if we're beyond 1 minute window
        }
      }

      // Process messages after target message
      for (var doc in afterMessages.docs) {
        final message = Message.fromDocument(doc);
        if (message.timestamp.difference(timestamp).inMinutes.abs() <= 1) {
          groupMessages.add(message);
        } else {
          break; // Stop if we're beyond 1 minute window
        }
      }

      // Sort all messages by timestamp
      groupMessages.sort((a, b) => a.timestamp.compareTo(b.timestamp));

      return groupMessages;
    } catch (e) {
      // print('Error getting image group messages: $e');
      return [targetMessage]; // Return only target message if error occurs
    }
  }

  /// Forwards a group of messages preserving their order and maintaining grouping for image messages
  /// Returns true if all messages were forwarded successfully
  static Future<bool> forwardMessageGroup({
    required List<Message> messages,
    required String toChatId,
    required String currentUserId,
    required String currentUserName,
    required CollectionReference chatsCollection,
    bool preserveGroup = false,
  }) async {
    try {
      // Base timestamp for maintaining order
      final baseTimestamp = DateTime.now();

      // Forward each message in the group
      String? imageGroupId;
      if (preserveGroup && messages.every((m) => m.type == MessageType.image)) {
        // Check if messages already have a common imageGroupId
        final existingGroupId = messages.first.metadata?['imageGroupId'];
        if (existingGroupId != null && 
            messages.every((m) => m.metadata?['imageGroupId'] == existingGroupId)) {
          // Use existing group ID pattern but generate new one for forwarded messages
          imageGroupId = 'fwd_${DateTime.now().millisecondsSinceEpoch}';
        } else {
          // Generate a new image group ID for this forwarded group
          imageGroupId = 'fwd_${DateTime.now().millisecondsSinceEpoch}';
        }
      }

      for (int i = 0; i < messages.length; i++) {
        final originalMessage = messages[i];
        // Generate unique message ID with small delay to ensure uniqueness
        await Future.delayed(const Duration(milliseconds: 1));
        final newMessageId = '${DateTime.now().millisecondsSinceEpoch}_$i';

        // Calculate time offset to preserve order but keep images grouped closely
        Duration timeOffset;
        if (originalMessage.type == MessageType.image) {
          // For images, use smaller time offset to maintain grouping (within 1 minute window)
          timeOffset = Duration(milliseconds: i * 50); // Even smaller offset for tighter grouping
        } else {
          // For other messages, use larger offset
          timeOffset = Duration(seconds: i);
        }

        // Create metadata for the forwarded message
        Map<String, dynamic> metadata = {};
        if (originalMessage.metadata != null) {
          metadata = Map<String, dynamic>.from(originalMessage.metadata!);
        }
        
        // Set the image group ID if preserving group and this is an image
        if (preserveGroup && imageGroupId != null && originalMessage.type == MessageType.image) {
          metadata['imageGroupId'] = imageGroupId;
          metadata['isForwardedGroup'] = true;
          metadata['originalGroupId'] = originalMessage.metadata?['imageGroupId'];
        }

        final forwardedMessage = originalMessage.copyWith(
          id: newMessageId,
          senderId: currentUserId,
          senderName: currentUserName,
          timestamp: baseTimestamp.add(timeOffset),
          isForwarded: true,
          status: MessageStatus.sent,
          replyToMessageId: null,
          metadata: metadata.isNotEmpty ? metadata : null,
        );

        await chatsCollection
            .doc(toChatId)
            .collection('messages')
            .doc(newMessageId)
            .set(forwardedMessage.toMap());

        // Small delay between message creations to ensure proper ordering
        await Future.delayed(const Duration(milliseconds: 10));
      }

      // Update last message with the last message in the group
      if (messages.isNotEmpty) {
        final lastOriginalMessage = messages.last;
        final lastMessageId = '${DateTime.now().millisecondsSinceEpoch}_${messages.length - 1}';
        
        // Create metadata for last message
        Map<String, dynamic> lastMetadata = {};
        if (lastOriginalMessage.metadata != null) {
          lastMetadata = Map<String, dynamic>.from(lastOriginalMessage.metadata!);
        }
        if (preserveGroup && imageGroupId != null && lastOriginalMessage.type == MessageType.image) {
          lastMetadata['imageGroupId'] = imageGroupId;
          lastMetadata['isForwardedGroup'] = true;
          lastMetadata['originalGroupId'] = lastOriginalMessage.metadata?['imageGroupId'];
        }

        final lastForwardedMessage = lastOriginalMessage.copyWith(
          id: lastMessageId,
          senderId: currentUserId,
          senderName: currentUserName,
          timestamp: baseTimestamp.add(Duration(milliseconds: (messages.length - 1) * 50)),
          isForwarded: true,
          status: MessageStatus.sent,
          replyToMessageId: null,
          metadata: lastMetadata.isNotEmpty ? lastMetadata : null,
        );

        await chatsCollection.doc(toChatId).update({
          'lastMessage': lastForwardedMessage.toMap(),
          'lastMessageTime': lastForwardedMessage.timestamp.millisecondsSinceEpoch,
          'updatedAt': DateTime.now().millisecondsSinceEpoch,
        });
      }

      return true;
    } catch (e) {
      print('Error forwarding message group: $e');
      return false;
    }
  }

  /// Groups messages from a list that belong to the same image group as the target message
  /// This is a more efficient version that works with an already loaded message list
  static List<Message> getImageGroupMessagesFromList({
    required List<Message> messages,
    required Message targetMessage,
  }) {
    if (targetMessage.type != MessageType.image) {
      return [targetMessage];
    }

    final messageIndex = messages.indexOf(targetMessage);
    if (messageIndex == -1) return [targetMessage];

    final timestamp = targetMessage.timestamp;
    List<Message> groupMessages = [];

    // Since messages are in descending order (newest first), we need to:
    // 1. Scan backwards (towards newer messages) from target
    // 2. Scan forwards (towards older messages) from target
    // 3. Sort the final result by timestamp to get chronological order

    // First, scan backwards to find newer images in the group
    var currentIndex = messageIndex - 1; // Start from message before target
    while (currentIndex >= 0) {
      final currentMessage = messages[currentIndex];
      if (currentMessage.senderId == targetMessage.senderId &&
          currentMessage.type == MessageType.image &&
          currentMessage.mediaUrl != null &&
          currentMessage.timestamp.difference(timestamp).inMinutes.abs() <= 1) {
        groupMessages.add(currentMessage);
        currentIndex--;
      } else {
        break;
      }
    }

    // Add the target message itself
    groupMessages.add(targetMessage);

    // Then, scan forwards to find older images in the group
    currentIndex = messageIndex + 1; // Start from message after target
    while (currentIndex < messages.length) {
      final currentMessage = messages[currentIndex];
      if (currentMessage.senderId == targetMessage.senderId &&
          currentMessage.type == MessageType.image &&
          currentMessage.mediaUrl != null &&
          timestamp.difference(currentMessage.timestamp).inMinutes.abs() <= 1) {
        groupMessages.add(currentMessage);
        currentIndex++;
      } else {
        break;
      }
    }

    // Sort by timestamp to get chronological order (oldest to newest)
    groupMessages.sort((a, b) => a.timestamp.compareTo(b.timestamp));
    return groupMessages;
  }
}
