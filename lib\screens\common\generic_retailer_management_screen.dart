import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:mr_garments_mobile/providers/generic_retailer_provider.dart';
import 'package:mr_garments_mobile/screens/common/generic_add_retailer_screen.dart';
import 'package:mr_garments_mobile/services/session_service.dart';

class GenericRetailerManagementScreen extends ConsumerStatefulWidget {
  final String companyType; // Should be 'distributor'

  const GenericRetailerManagementScreen({
    super.key,
    required this.companyType,
  });

  @override
  ConsumerState<GenericRetailerManagementScreen> createState() =>
      _GenericRetailerManagementScreenState();
}

class _GenericRetailerManagementScreenState
    extends ConsumerState<GenericRetailerManagementScreen> {
  final TextEditingController _searchController = TextEditingController();
  Timer? _debounce;
  int? _companyId;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);
    _loadCompanyId();
  }

  Future<void> _loadCompanyId() async {
    final userId = await SessionService.getUserId();
    if (userId != null) {
      setState(() {
        _companyId = userId;
      });
      _refreshRetailerList();
    }
  }

  @override
  void dispose() {
    _debounce?.cancel();
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      setState(() {
        _searchQuery = _searchController.text.toLowerCase().trim();
      });
    });
  }

  void _refreshRetailerList() {
    if (_companyId != null) {
      ref.read(genericRetailerProvider.notifier).fetchRetailersByDistributor(_companyId!);
    }
  }

  String get _companyDisplayName {
    switch (widget.companyType.toLowerCase()) {
      case 'distributor':
        return 'Distributor';
      default:
        return 'Company';
    }
  }

  List<dynamic> _filterRetailers(List<dynamic> retailers) {
    if (_searchQuery.isEmpty) {
      return retailers;
    }
    
    return retailers.where((retailer) {
      final name = retailer['name']?.toString().toLowerCase() ?? '';
      final email = retailer['email']?.toString().toLowerCase() ?? '';
      final mobile = retailer['mobile']?.toString().toLowerCase() ?? 
                    retailer['mobile_number']?.toString().toLowerCase() ?? '';
      final companyName = retailer['company_name']?.toString().toLowerCase() ?? '';
      
      return name.contains(_searchQuery) || 
             email.contains(_searchQuery) || 
             mobile.contains(_searchQuery) ||
             companyName.contains(_searchQuery);
    }).toList();
  }

  Widget _buildAppBar() {
    return AppBar(
      backgroundColor: const Color(0xFF005368),
      foregroundColor: Colors.white,
      title: Text(
        '$_companyDisplayName Retailers',
        style: GoogleFonts.poppins(
          fontWeight: FontWeight.w600,
          fontSize: 20,
        ),
      ),
      actions: [
        IconButton(
          onPressed: _refreshRetailerList,
          icon: const Icon(LucideIcons.refreshCw),
        ),
      ],
    );
  }

  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search retailers...',
          hintStyle: GoogleFonts.poppins(color: Colors.grey[500]),
          prefixIcon: Icon(LucideIcons.search, color: Colors.grey[500]),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        ),
        style: GoogleFonts.poppins(),
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (_) => GenericAddRetailerScreen(
              companyType: widget.companyType,
            ),
          ),
        ).then((_) => _refreshRetailerList());
      },
      backgroundColor: const Color(0xFF005368),
      child: const Icon(LucideIcons.plus, color: Colors.white),
    );
  }

  Widget _buildRetailerCard(Map<String, dynamic> retailer) {
    final status = 'approved';
    
    // Determine status color and text based on status
    // Color statusColor;
    // String statusText;
    
    // switch (status) {
    //   case 'approved':
    //     statusColor = Colors.green;
    //     statusText = 'Active';
    //     break;
    //   case 'deactivated':
    //     statusColor = Colors.red;
    //     statusText = 'Deactivated';
    //     break;
    //   default:
    //     statusColor = Colors.orange;
    //     statusText = 'Pending Approval';
    //     break;
    // }

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: const Color(0xFF005368),
                  child: Text(
                    retailer['name']?.toString().substring(0, 1).toUpperCase() ?? 'R',
                    style: GoogleFonts.poppins(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        retailer['name'] ?? 'Unknown',
                        style: GoogleFonts.poppins(
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                      if (retailer['company_name'] != null)
                        Text(
                          retailer['company_name'],
                          style: GoogleFonts.poppins(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                      Text(
                        retailer['email'] ?? '',
                        style: GoogleFonts.poppins(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                // Container(
                //   padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                //   decoration: BoxDecoration(
                //     color: statusColor.withValues(alpha: 0.1),
                //     borderRadius: BorderRadius.circular(20),
                //     border: Border.all(color: statusColor.withValues(alpha: 0.3)),
                //   ),
                //   child: Text(
                //     statusText,
                //     style: GoogleFonts.poppins(
                //       color: statusColor,
                //       fontSize: 12,
                //       fontWeight: FontWeight.w500,
                //     ),
                //   ),
                // ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(LucideIcons.phone, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 8),
                Text(
                  retailer['mobile'] ?? retailer['mobile_number'] ?? 'No phone',
                  style: GoogleFonts.poppins(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
                const Spacer(),
                if (retailer['created_at'] != null) ...[
                  Icon(LucideIcons.calendar, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 8),
                  Text(
                    'Joined: ${retailer['created_at'].toString().substring(0, 10)}',
                    style: GoogleFonts.poppins(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                ],
              ],
            ),
            // if (status == 'approved') ...[
            //   const SizedBox(height: 12),
            //   Row(
            //     children: [
            //       Expanded(
            //         child: OutlinedButton.icon(
            //           onPressed: () {
            //             Navigator.push(
            //               context,
            //               MaterialPageRoute(
            //                 builder: (_) => EditRetailerScreen(retailer: retailer),
            //               ),
            //             ).then((_) => _refreshRetailerList());
            //           },
            //           icon: const Icon(LucideIcons.edit, size: 16),
            //           label: Text(
            //             'Edit',
            //             style: GoogleFonts.poppins(fontSize: 14),
            //           ),
            //           style: OutlinedButton.styleFrom(
            //             foregroundColor: const Color(0xFF005368),
            //             side: const BorderSide(color: Color(0xFF005368)),
            //             shape: RoundedRectangleBorder(
            //               borderRadius: BorderRadius.circular(8),
            //             ),
            //           ),
            //         ),
            //       ),
            //     ],
            //   ),
            // ],
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) => Scaffold(
    backgroundColor: Colors.grey[50],
    appBar: PreferredSize(
      preferredSize: const Size.fromHeight(kToolbarHeight),
      child: _buildAppBar(),
    ),
    floatingActionButton: _buildFloatingActionButton(),
    body: Column(
      children: [
        _buildSearchBar(),
        Expanded(
          child: Consumer(
            builder: (context, ref, child) {
              final retailerState = ref.watch(genericRetailerProvider);
              
              return retailerState.retailers.when(
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (error, stack) => Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        LucideIcons.alertCircle,
                        size: 64,
                        color: Colors.red[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Error loading retailers',
                        style: GoogleFonts.poppins(
                          fontSize: 18,
                          color: Colors.red[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        error.toString(),
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          color: Colors.grey[500],
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _refreshRetailerList,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                ),
                data: (retailers) {
                  final filteredRetailers = _filterRetailers(retailers);
                  
                  if (filteredRetailers.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            LucideIcons.store,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            _searchQuery.isEmpty 
                              ? 'No retailers found'
                              : 'No retailers match your search',
                            style: GoogleFonts.poppins(
                              fontSize: 18,
                              color: Colors.grey[600],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            _searchQuery.isEmpty 
                              ? 'Add your first retailer to get started'
                              : 'Try searching with a different term',
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              color: Colors.grey[500],
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  return ListView.builder(
                    itemCount: filteredRetailers.length,
                    itemBuilder: (context, index) {
                      return _buildRetailerCard(filteredRetailers[index]);
                    },
                  );
                },
              );
            },
          ),
        ),
      ],
    ),
  );
}
