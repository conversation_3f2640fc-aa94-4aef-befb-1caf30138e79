import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:mr_garments_mobile/services/session_service.dart';
import 'package:mr_garments_mobile/utils/snackbar.dart';
import 'package:share_plus/share_plus.dart';

class MorePage extends StatefulWidget {
  const MorePage({super.key});

  @override
  State<MorePage> createState() => _MorePageState();
}

class _MorePageState extends State<MorePage> {
  String? _currentUserName;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUserInfo();
  }

  Future<void> _loadUserInfo() async {
    try {
      setState(() => _isLoading = true);

      final userName = await SessionService.getUserName();

      setState(() {
        _currentUserName = userName;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
    }
  }

  // void _editProfile() {
  //   AppSnackbar.showInfo(context, 'Edit profile feature coming soon');
  // }

  // void _changePassword() {
  //   AppSnackbar.showInfo(context, 'Change password feature coming soon');
  // }

  // void _chatBackups() {
  //   AppSnackbar.showInfo(context, 'Chat backups feature coming soon');
  // }

  void _privacyPolicies() {
    AppSnackbar.showInfo(context, 'Privacy policies feature coming soon');
  }

  void _termsAndConditions() {
    AppSnackbar.showInfo(context, 'Terms & conditions feature coming soon');
  }

  void _helpAndSupport() {
    AppSnackbar.showInfo(context, 'Help and support feature coming soon');
  }

  Future<void> _inviteFriend() async {
    try {
      const appDescription =
          'MR Garments - the best app for garment business management!';
      const downloadLink =
          'https://play.google.com/store/apps/details?id=com.braincave.mrgarments';

      const shareText = '''$appDescription

🔗 Download the app: $downloadLink

#MRGarments #GarmentBusiness #BusinessApp''';

      // Use native share dialog
      await Share.share(shareText);

      // Show success feedback
      if (mounted) {
        AppSnackbar.showSuccess(context, 'Share dialog opened successfully!');
      }
    } catch (e) {
      if (mounted) {
        // Fallback to clipboard if sharing fails
        try {
          const appDescription =
              'MR Garments - the best app for garment business management!';
          const downloadLink =
              'https://play.google.com/store/apps/details?id=com.braincave.mrgarments';
          const shareText =
              '$appDescription\n\nDownload the app: $downloadLink';

          await Clipboard.setData(ClipboardData(text: shareText));
          AppSnackbar.showInfo(
            context,
            'Share failed. App link copied to clipboard instead.',
          );
        } catch (clipboardError) {
          AppSnackbar.showError(context, 'Error sharing app: $e');
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        backgroundColor: Colors.white,
        body: Center(
          child: CircularProgressIndicator(color: Color(0xFF005368)),
        ),
      );
    }

    return Scaffold(
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 20),

            // Header
            _buildHeader(),

            const SizedBox(height: 30),

            // Main Options Section
            // _buildSection(
            //   title: 'Account & Settings',
            //   items: [
            //     _buildMenuItem(
            //       icon: LucideIcons.userCircle,
            //       title: 'Edit Profile',
            //       subtitle: 'Update your profile information',
            //       onTap: _editProfile,
            //     ),
            //     _buildMenuItem(
            //       icon: LucideIcons.lock,
            //       title: 'Change Password',
            //       subtitle: 'Update your account password',
            //       onTap: _changePassword,
            //     ),
            //     _buildMenuItem(
            //       icon: LucideIcons.database,
            //       title: 'Chat Backups',
            //       subtitle: 'Backup and restore your chats',
            //       onTap: _chatBackups,
            //     ),
            //   ],
            // ),

            // const SizedBox(height: 30),

            // Legal & Support Section
            _buildSection(
              title: 'Legal & Support',
              items: [
                _buildMenuItem(
                  icon: LucideIcons.shield,
                  title: 'Privacy Policies',
                  subtitle: 'Read our privacy policies',
                  onTap: _privacyPolicies,
                ),
                _buildMenuItem(
                  icon: LucideIcons.fileText,
                  title: 'Terms & Conditions',
                  subtitle: 'View terms and conditions',
                  onTap: _termsAndConditions,
                ),
                _buildMenuItem(
                  icon: LucideIcons.helpCircle,
                  title: 'Help and Support',
                  subtitle: 'Get help and support',
                  onTap: _helpAndSupport,
                ),
              ],
            ),

            const SizedBox(height: 30),

            // Share Section
            _buildSection(
              title: 'Share',
              items: [
                _buildMenuItem(
                  icon: LucideIcons.userPlus,
                  title: 'Invite Friend',
                  subtitle: 'Share MR Garments app with friends',
                  onTap: _inviteFriend,
                ),
              ],
            ),

            const SizedBox(height: 50),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF005368), Color(0xFF007A8C)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              LucideIcons.moreHorizontal,
              color: Colors.white,
              size: 28,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'More Options',
                  style: GoogleFonts.poppins(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                Text(
                  'Manage your chat experience',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection({required String title, required List<Widget> items}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF005368),
          ),
        ),
        const SizedBox(height: 16),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                spreadRadius: 1,
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(children: items),
        ),
      ],
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool isDestructive = false,
    Color? iconColor,
  }) {
    final effectiveIconColor =
        isDestructive ? Colors.red : iconColor ?? const Color(0xFF005368);

    return ListTile(
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      leading: Container(
        width: 45,
        height: 45,
        decoration: BoxDecoration(
          color: effectiveIconColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Icon(icon, color: effectiveIconColor, size: 22),
      ),
      title: Text(
        title,
        style: GoogleFonts.poppins(
          fontSize: 15,
          fontWeight: FontWeight.w500,
          color: isDestructive ? Colors.red : Colors.black87,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: GoogleFonts.poppins(fontSize: 13, color: Colors.grey[600]),
      ),
      trailing: Icon(
        LucideIcons.chevronRight,
        color: Colors.grey[400],
        size: 20,
      ),
    );
  }
}
